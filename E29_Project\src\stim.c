/**************************************************************************************************/
/**
 * @file     stim.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#include "stim.h"

/* STIM configuration */
static const STIM_Config_t stimConfig = 
{
    .workMode = STIM_FREE_COUNT,
    /*counter clock is 20M, compare value = 2000000,  period = 0.1s*/
    .compareValue = 2000000,
    .countResetMode = STIM_INCREASE_FROM_0,
    .clockSource = STIM_FUNCTION_CLOCK,
};

static void STIM_IntCallBack(void)
{
		//printf("hello \n");
}

void Stim_Init(void)
{
		CLK_ModuleSrc(CLK_STIM, CLK_SRC_OSC40M);
    CLK_SetClkDivider(CLK_STIM, CLK_DIV_2);
    SYSCTRL_EnableModule(SYSCTRL_STIM);
                                                       
    /* Init STIM_0*/
    STIM_Init(STIM_0,&stimConfig);
	
		/* Install interrupt callback function */
		STIM_InstallCallBackFunc(STIM_0,STIM_INT,STIM_IntCallBack);
    /* Enable STIM_0 interrupt*/
    STIM_IntCmd(STIM_0, ENABLE);
    
    /* Enable STIM */
    STIM_Enable(STIM_0);
		/* Enable STIM NVIC IRQ*/
    NVIC_EnableIRQ(STIM_IRQn);
}	
 