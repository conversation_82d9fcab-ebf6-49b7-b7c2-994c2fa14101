#ifndef _SCH_CFG_H_
#define _SCH_CFG_H_

#include "stdbool.h"
#include "app.h"

#define SCH_PERIODIC_MAX_NUM                                     6
#define SCH_1MSCOUNTER(Clock)                         (Clock*1000)             
#define SCH_EndOfList                                (Sch_Task_T)0
#define SCH_MS(Ms)                                              Ms
#define SCH_TIMEDURATION(Idx)           (SCH_1MSCOUNTER(64)*(Idx))

typedef struct TaskTablePara
{
    uint8_t  u8CurExecuteIdx;
    uint8_t  u8TaskStartPosition;
    uint8_t  u8TaskEndPosition;
    uint32_t u32TimeDuration;
}TaskTablePara;

extern uint8_t u8UartUpdateState;

extern bool boTimer_200ms;
extern bool boTimer_2ms;
extern bool boTimer_100ms;
extern bool boTimer_5ms;


extern void Sch_Main(void);

extern const Sch_Task_Info_T Sch_RunTaskTable[]; 

#endif
