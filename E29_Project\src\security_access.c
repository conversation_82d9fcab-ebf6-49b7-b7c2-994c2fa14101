/**************************************************************************************************/
/**
 * @file      : security_access.c
 * @brief     : Security access protocol module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "security_access.h"
#include "factory_mode.h"
#include "can.h"

/* Security access state variable */
static TestProtocolState_t test_state = Security_IDLE;

/* Security access state variables */
static volatile uint8_t received_seed[16] = {0};  /* Store received 16-byte seed */

/* Security access protocol data */
static uint8_t security_request_seed[64] = {
    0xAA,0xBB, 0xCC, 0xDD, 0xEE,  /* Request seed command (5bytes) */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00         
};

static uint8_t security_send_key[64] = {
    0xEE, 0xDD, 0xCC, 0xBB, 0xAA,  /* Send key command (5 bytes) */
    0x00,                          /* Calculated seed (1 byte) - will be calculated */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00              /* 8 zeros, total 58 zeros */
};

static uint8_t negative_response[64] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00                                        /* 4 zeros, total 64 zeros */
};

/**
 * @brief Initialize security access module
 */
void Security_Access_Init(void)
{
    test_state = Security_IDLE;
    /* Clear received seed buffer */
    for(int i = 0; i < 16; i++) {
        received_seed[i] = 0;
    }
}

/**
 * @brief Calculate simple seed for security access
 * @param seed_data: Pointer to received seed data
 * @return Calculated seed value
 */
uint8_t Calculate_Security_Seed(const uint8_t *seed_data)
{
    /* Simple calculation: XOR all 16 bytes of received seed */
    uint8_t result = 0;
    for(int i = 0; i < 16; i++) {
        result ^= seed_data[i];
    }
    return result;
}

/**
 * @brief Prepare security key command with calculated seed
 */
void Prepare_Security_Key_Command(void)
{
    /* Calculate seed from received data */
    uint8_t calculated_seed = Calculate_Security_Seed(received_seed);

    /* Fill the calculated seed into position 5 (after EE DD CC BB AA) */
    security_send_key[5] = calculated_seed;
}

/**
 * @brief Enter security mode
 */
void Enter_Security_Mode(void)
{
    /* Send request seed command: AA BB CC DD EE */
    CAN_Send_FactoryMode_Msg(0x5F0, security_request_seed);
    test_state = Security_Step1_Sent;
}

/**
 * @brief Handle security access protocol response
 * @param response_data: Pointer to response data
 * @param data_len: Length of response data
 */
void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len)
{
    switch(test_state) {
        case Security_Step1_Sent:
            /* Expect: 99 + 16-byte seed + 47 zeros */
            if(response_data[0] == 0x99) {
                /* Store received 16-byte seed (bytes 1-16) */
                for(int i = 0; i < 16; i++) {
                    received_seed[i] = response_data[i + 1];
                }
                /* Prepare and send security key */
                Prepare_Security_Key_Command();
                CAN_Send_FactoryMode_Msg(0x5F0, security_send_key);
                test_state = Security_Step2_Sent;
            }
            break;

        case Security_Step2_Sent:
            /* Handle security access verification result */
            if(response_data[0] == 0xAA) {
                /* AA response: Security access successful, enter factory mode */
                Enter_Factory_Mode();
                test_state = Security_Complete ;
            } else if(response_data[0] == 0xBB) {
                /* BB response: Security access failed, send negative response */
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            } else {
                /* Other responses: Send negative response */
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            }
            break;

        default:
            break;
    }
}

/**
 * @brief Get current security access state
 * @return Current security access state
 */
TestProtocolState_t Get_Security_Access_State(void)
{
    return test_state;
}
