/**************************************************************************************************/
/**
 * @file      : security_access.c
 * @brief     : Security access protocol module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "security_access.h"
#include "factory_mode.h"
#include "can.h"

/* Security access state variable */
static TestProtocolState_t test_state = Security_IDLE;

/* Security access state variables */
static volatile uint8_t received_seed[16] = {0};  /* Store received 16-byte seed */

/* AES128-CMAC key as specified in requirements */
static const uint8_t aes128_key[16] = {
    0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
    0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
};

/* Security access protocol data */
static uint8_t security_request_seed[64] = {
    0xAA,0xBB, 0xCC, 0xDD, 0xEE,  /* Request seed command (5bytes) */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00         
};

static uint8_t security_send_key[64] = {
    0xEE, 0xDD, 0xCC, 0xBB, 0xAA,  /* Send key command (5 bytes) */
    /* Next 16 bytes will be filled with AES128-CMAC calculated key */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    /* Remaining 43 bytes filled with zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

static uint8_t negative_response[64] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00                                        /* 4 zeros, total 64 zeros */
};

/**
 * @brief Initialize security access module
 */
void Security_Access_Init(void)
{
    test_state = Security_IDLE;
    /* Clear received seed buffer */
    for(int i = 0; i < 16; i++) {
        received_seed[i] = 0;
    }
}

/* AES S-box for encryption */
static const uint8_t sbox[256] = {
    0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5, 0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
    0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0, 0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
    0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc, 0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
    0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a, 0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
    0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0, 0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
    0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b, 0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
    0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85, 0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
    0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5, 0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
    0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17, 0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
    0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88, 0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
    0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c, 0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
    0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9, 0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
    0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6, 0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
    0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e, 0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
    0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94, 0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
    0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68, 0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16
};

/* Round constants for AES key expansion */
static const uint8_t rcon[11] = {
    0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36
};

/**
 * @brief XOR two 16-byte blocks
 */
static void xor_block(const uint8_t *a, const uint8_t *b, uint8_t *result)
{
    for(int i = 0; i < 16; i++) {
        result[i] = a[i] ^ b[i];
    }
}

/**
 * @brief Left shift a 16-byte block by 1 bit
 */
static void left_shift_block(const uint8_t *input, uint8_t *output)
{
    uint8_t carry = 0;
    for(int i = 15; i >= 0; i--) {
        uint8_t new_carry = (input[i] & 0x80) ? 1 : 0;
        output[i] = (input[i] << 1) | carry;
        carry = new_carry;
    }
}

/**
 * @brief Simplified AES128-like encryption for one block
 * Note: This is a simplified implementation for demonstration.
 * In production, use a proper AES library.
 * @param key: 16-byte AES key
 * @param input: 16-byte input block
 * @param output: 16-byte output block
 */
void AES128_Encrypt_Block(const uint8_t *key, const uint8_t *input, uint8_t *output)
{
    uint8_t state[16];

    /* Copy input to state */
    for(int i = 0; i < 16; i++) {
        state[i] = input[i];
    }

    /* Simplified encryption using multiple rounds of operations */
    for(int round = 0; round < 10; round++) {
        /* XOR with key */
        for(int i = 0; i < 16; i++) {
            state[i] ^= key[i] ^ rcon[round + 1];
        }

        /* SubBytes using S-box */
        for(int i = 0; i < 16; i++) {
            state[i] = sbox[state[i]];
        }

        /* Simple mixing operation */
        for(int i = 0; i < 16; i++) {
            state[i] ^= state[(i + 1) % 16] ^ state[(i + 4) % 16];
        }
    }

    /* Final key addition */
    for(int i = 0; i < 16; i++) {
        output[i] = state[i] ^ key[i];
    }
}

/**
 * @brief Generate CMAC subkeys K1 and K2
 * @param key: 16-byte AES key
 * @param k1: Output subkey K1
 * @param k2: Output subkey K2
 */
void Generate_Subkeys(const uint8_t *key, uint8_t *k1, uint8_t *k2)
{
    uint8_t zero_block[16] = {0};
    uint8_t l[16];

    /* L = AES_K(0^128) */
    AES128_Encrypt_Block(key, zero_block, l);

    /* Generate K1 */
    left_shift_block(l, k1);
    if(l[0] & 0x80) {
        k1[15] ^= 0x87;  /* XOR with Rb */
    }

    /* Generate K2 */
    left_shift_block(k1, k2);
    if(k1[0] & 0x80) {
        k2[15] ^= 0x87;  /* XOR with Rb */
    }
}

/**
 * @brief AES128-CMAC calculation
 * @param key: 16-byte AES key
 * @param message: Input message
 * @param message_len: Message length
 * @param mac: Output MAC (16 bytes)
 */
void AES128_CMAC(const uint8_t *key, const uint8_t *message, uint16_t message_len, uint8_t *mac)
{
    uint8_t k1[16], k2[16];
    uint8_t x[16] = {0};
    uint8_t y[16];

    /* Generate subkeys */
    Generate_Subkeys(key, k1, k2);

    /* Process complete blocks */
    uint16_t complete_blocks = message_len / 16;
    for(uint16_t i = 0; i < complete_blocks; i++) {
        xor_block(x, &message[i * 16], y);
        AES128_Encrypt_Block(key, y, x);
    }

    /* Process last block */
    uint8_t last_block[16] = {0};
    uint16_t remaining = message_len % 16;

    if(remaining == 0 && message_len > 0) {
        /* Complete last block */
        for(int i = 0; i < 16; i++) {
            last_block[i] = message[(complete_blocks - 1) * 16 + i];
        }
        xor_block(last_block, k1, last_block);
    } else {
        /* Incomplete last block - apply padding */
        for(uint16_t i = 0; i < remaining; i++) {
            last_block[i] = message[complete_blocks * 16 + i];
        }
        if(remaining < 16) {
            last_block[remaining] = 0x80;  /* Padding */
        }
        xor_block(last_block, k2, last_block);
    }

    xor_block(x, last_block, y);
    AES128_Encrypt_Block(key, y, mac);
}

/**
 * @brief Calculate security key using AES128-CMAC
 * @param seed_data: Pointer to received 16-byte seed data
 * @param key_output: Output buffer for calculated key (16 bytes)
 */
void Calculate_Security_Key_AES128_CMAC(const uint8_t *seed_data, uint8_t *key_output)
{
    /* Use AES128-CMAC to calculate the security key from the seed */
    AES128_CMAC(aes128_key, seed_data, 16, key_output);
}

/**
 * @brief Prepare security key command with calculated key
 */
void Prepare_Security_Key_Command(void)
{
    uint8_t calculated_key[16];

    /* Calculate key using AES128-CMAC */
    Calculate_Security_Key_AES128_CMAC(received_seed, calculated_key);

    /* Fill the calculated key into security_send_key starting at position 5 */
    /* First 5 bytes: EE DD CC BB AA, then 16 bytes of calculated key */
    for(int i = 0; i < 16; i++) {
        security_send_key[5 + i] = calculated_key[i];
    }
}

/**
 * @brief Enter security mode
 */
void Enter_Security_Mode(void)
{
    /* Send request seed command: AA BB CC DD EE */
    CAN_Send_FactoryMode_Msg(0x5F0, security_request_seed);
    test_state = Security_Step1_Sent;
}

/**
 * @brief Handle security access protocol response
 * @param response_data: Pointer to response data
 * @param data_len: Length of response data
 */
void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len)
{
    switch(test_state) {
        case Security_Step1_Sent:
            /* Expect: 99 + 16-byte seed + 47 zeros */
            if(response_data[0] == 0x99) {
                /* Store received 16-byte seed (bytes 1-16) */
                for(int i = 0; i < 16; i++) {
                    received_seed[i] = response_data[i + 1];
                }
                /* Prepare and send security key */
                Prepare_Security_Key_Command();
                CAN_Send_FactoryMode_Msg(0x5F0, security_send_key);
                test_state = Security_Step2_Sent;
            }
            break;

        case Security_Step2_Sent:
            /* Handle security access verification result */
            if(response_data[0] == 0xAA) {
                /* AA response: Security access successful, enter factory mode */
                Enter_Factory_Mode();
                test_state = Security_Complete ;
            } else if(response_data[0] == 0xBB) {
                /* BB response: Security access failed, send negative response */
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            } else {
                /* Other responses: Send negative response */
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            }
            break;

        default:
            break;
    }
}

/**
 * @brief Get current security access state
 * @return Current security access state
 */
TestProtocolState_t Get_Security_Access_State(void)
{
    return test_state;
}

/**
 * @brief Test function to verify AES128-CMAC implementation
 * This function can be called during development to verify the algorithm
 */
void Test_AES128_CMAC_Implementation(void)
{
    /* Test with known values */
    uint8_t test_seed[16] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    };

    uint8_t calculated_key[16];

    /* Calculate key using our implementation */
    Calculate_Security_Key_AES128_CMAC(test_seed, calculated_key);

    /* The calculated_key now contains the AES128-CMAC result */
    /* In a real implementation, you would compare this with expected values */
    /* For debugging, you can examine calculated_key in debugger */

    /* Example: Print first few bytes for verification (if UART available) */
    // printf("Calculated key: %02X %02X %02X %02X...\n",
    //        calculated_key[0], calculated_key[1], calculated_key[2], calculated_key[3]);
}
