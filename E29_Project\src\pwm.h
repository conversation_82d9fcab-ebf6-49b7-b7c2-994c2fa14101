/**************************************************************************************************/
/**
 * @file     pwm.h
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#ifndef PWM_H
#define PWM_H

#include "Z20K11xM_tim.h"
#include "Z20K11xM_drv.h"
#include "Z20K11xM_gpio.h"
#include "Z20K11xM_sysctrl.h"
#include "Z20K11xM_clock.h"
#include "common_func.h"

extern void Pwm_Init(void);
extern void Ex_PWMChangeDutyCycle(uint8_t BacklightLevel);

#endif /* PWM_H */

