/**************************************************************************************************/
/**
 * @file      : test_aes_cmac.c
 * @brief     : Test program for AES128-CMAC implementation
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @note      : This file contains test functions to verify AES128-CMAC implementation
 *              Can be used during development and debugging
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "aes128_cmac.h"
#include "security_access.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief Print hex data for debugging
 * @param data: Data to print
 * @param len: Length of data
 * @param label: Label for the data
 */
void print_hex_data(const uint8_t *data, size_t len, const char *label)
{
    printf("%s: ", label);
    for(size_t i = 0; i < len; i++) {
        printf("%02X ", data[i]);
        if((i + 1) % 16 == 0) printf("\n");
    }
    if(len % 16 != 0) printf("\n");
}

/**
 * @brief Test AES128-CMAC with known test vectors
 */
void test_aes_cmac_vectors(void)
{
    printf("\n=== AES128-CMAC Test Vectors ===\n");
    
    /* Test Case 1: Empty message */
    uint8_t key1[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };
    
    uint8_t expected1[16] = {
        0xbb, 0x1d, 0x69, 0x29, 0xe9, 0x59, 0x37, 0x28,
        0x7f, 0xa3, 0x7d, 0x12, 0x9b, 0x75, 0x67, 0x46
    };
    
    uint8_t result1[16];
    
    AES128_CMAC_Calculate(key1, NULL, 0, result1);
    
    print_hex_data(key1, 16, "Test Key 1");
    print_hex_data(expected1, 16, "Expected MAC 1");
    print_hex_data(result1, 16, "Calculated MAC 1");
    
    /* Compare first 4 bytes */
    int match1 = 1;
    for(int i = 0; i < 4; i++) {
        if(result1[i] != expected1[i]) {
            match1 = 0;
            break;
        }
    }
    printf("Test 1 (Empty message): %s\n", match1 ? "PASS" : "FAIL");
}

/**
 * @brief Test our specific security access implementation
 */
void test_security_access_implementation(void)
{
    printf("\n=== Security Access Implementation Test ===\n");
    
    /* Our specific key */
    uint8_t our_key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52, 
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };
    
    /* Test seed (simulating ECU response) */
    uint8_t test_seed[16] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    };
    
    uint8_t calculated_key[16];
    
    /* Test our security access function */
    Calculate_Security_Key_AES128_CMAC(test_seed, calculated_key);
    
    print_hex_data(our_key, 16, "AES Key");
    print_hex_data(test_seed, 16, "Test Seed");
    print_hex_data(calculated_key, 16, "Calculated Security Key");
    
    /* Test with different seed */
    uint8_t test_seed2[16] = {
        0xFF, 0xEE, 0xDD, 0xCC, 0xBB, 0xAA, 0x99, 0x88,
        0x77, 0x66, 0x55, 0x44, 0x33, 0x22, 0x11, 0x00
    };
    
    uint8_t calculated_key2[16];
    Calculate_Security_Key_AES128_CMAC(test_seed2, calculated_key2);
    
    print_hex_data(test_seed2, 16, "Test Seed 2");
    print_hex_data(calculated_key2, 16, "Calculated Security Key 2");
    
    /* Verify keys are different (they should be) */
    int keys_different = 0;
    for(int i = 0; i < 16; i++) {
        if(calculated_key[i] != calculated_key2[i]) {
            keys_different = 1;
            break;
        }
    }
    
    printf("Different seeds produce different keys: %s\n", keys_different ? "PASS" : "FAIL");
}

/**
 * @brief Main test function
 */
void run_all_aes_cmac_tests(void)
{
    printf("\n========================================\n");
    printf("    AES128-CMAC Implementation Test\n");
    printf("========================================\n");
    
    /* Run library test */
    int lib_test_result = AES128_CMAC_Test();
    printf("Library Test Result: %s\n", lib_test_result == 0 ? "PASS" : "FAIL");
    
    /* Run test vectors */
    test_aes_cmac_vectors();
    
    /* Test our implementation */
    test_security_access_implementation();
    
    /* Run security access test */
    Test_AES128_CMAC_Implementation();
    
    printf("\n========================================\n");
    printf("    All Tests Completed\n");
    printf("========================================\n");
}

/* Uncomment the main function below for standalone testing */
/*
int main(void)
{
    run_all_aes_cmac_tests();
    return 0;
}
*/
