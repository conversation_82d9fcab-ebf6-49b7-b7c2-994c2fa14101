/**************************************************************************************************/
/**
 * @file     common_func.h
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#ifndef COMMON_FUNC_H
#define COMMON_FUNC_H

#include "Z20K11xM_drv.h"

#define MAX(a, b) ((a) > (b) ? (a) : (b))
#define MIN(a, b) ((a) < (b) ? (a) : (b))

extern void delay(volatile int cycles);
extern uint8_t CalculateChecksum(uint8_t StartByte, uint8_t EndByte, uint8_t* Data);
extern void FillZeroToArray(uint8_t StartByte, uint8_t EndByte, uint8_t* Data);

#endif /* COMMON_FUNC_H */
