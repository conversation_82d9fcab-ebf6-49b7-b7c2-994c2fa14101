/**************************************************************************************************/
/**
 * @file     i2c.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/

#include "i2c.h"
#include "uart.h"

bool boI2c0IsIdle = true;

//i2c[]
uint8_t u8ModeRecvBuf[8] = {0};
uint8_t u8ModeRecvBufSN = 0;

uint8_t u8BKLRecvBuf[8] = {0};
uint8_t u8BKLRecvBufSN = 0;

uint8_t u8FaultRecvBuf[16] = {0};
uint8_t u8FaultRecvBufSN = 0;

uint8_t u8HWRecvBuf[16] = {0};
uint8_t u8HWRecvBufSN = 0;

uint8_t u8SWRecvBuf[16] = {0};
uint8_t u8SWRecvBufSN = 0;

uint8_t u8PartNumRecvBuf[32] = {0};
uint8_t u8PartNumRecvBufSN = 0;

uint8_t u8TempRecvBuf[8] = {0};
uint8_t u8TempRecvBufSN = 0;

uint8_t u8ProRecvBuf[16] = {0};
uint8_t u8ProRecvBufSN = 0;

uint8_t u8UpdateRecvBuf[32] = {0};
uint8_t u8UpdateRecvBufSN = 0;

uint8_t u8BLKREGBuf[8] = {0};
uint8_t u8BLKREGBufSN = 0;

uint8_t u8DesFaultBuf[8] = {0};
uint8_t u8DesFaultBufSN = 0;

extern uint8_t u8FrameidLength;
extern uint8_t u8MstRecvAckBuf[];
extern uint8_t u8Check_Flag; 

I2C_Config_t slaveConfig =
{
    .masterSlaveMode = I2C_SLAVE,       /* I2C mode: slave */
    .speedMode = I2C_SPEED_FAST,   			/* Speed mode: fast mode 400kb/s */
    .addrBitMode = I2C_ADDR_BITS_7,    	/* Address mode: 7 bits */
    .ownSlaveAddr = 0x00000077,         /* Own slave address:0x00000077 */
    .restart = DISABLE                  /* Determines whether RESTART condition may be sent when acting as a master: enable */
};

I2C_Config_t masterConfig =
{
    .masterSlaveMode = I2C_MASTER,      /* I2C mode: master */
    .speedMode = I2C_SPEED_FAST,    /* Speed mode: standard mode 400kb/s */
    .addrBitMode = I2C_ADDR_BITS_7,    	/* Address mode: 7 bits */
    .ownSlaveAddr = 0x00000040,         /* Own slave address:0x00000090 */
    .restart = ENABLE                   /* Determines whether RESTART condition may be sent when acting as a master: enable */
};

I2C_Config_t masterConfig1 =
{
    .masterSlaveMode = I2C_MASTER,      /* I2C mode: master */
    .speedMode = I2C_SPEED_FAST,    /* Speed mode: standard mode 400kb/s */
    .addrBitMode = I2C_ADDR_BITS_7,    	/* Address mode: 7 bits */
    .ownSlaveAddr = 0x00000055,         /* Own slave address:0x00000090 */
    .restart = ENABLE                   /* Determines whether RESTART condition may be sent when acting as a master: enable */
};

const I2C_FifoConfig_t slaveFifoConfig =
{
    .recvFifoThr = 0,       /* Receive  FIFO threshold level:3 */
    .transmitFifoThr = 2    /* Transmit FIFO threshold level:3 */
};

const I2C_FifoConfig_t masterFifoConfig =
{
    .recvFifoThr = 0,       /* Receive  FIFO threshold level:3 */
    .transmitFifoThr = 0    /* Transmit FIFO threshold level:3 */
};

static void MstRecvByType(I2C_Id_t i2cNo, uint16_t len, I2C_RestartStop_t restartStopType)
{
    /* When TX FIFO is not full, the master sends a command to read */
    while(RESET == I2C_GetStatus(i2cNo, I2C_STATUS_TFNF));
    I2C_MasterReadCmd(i2cNo, restartStopType);
}

static void MstSendByType(I2C_Id_t i2cNo, uint8_t * gTxBuffer, uint16_t len, I2C_RestartStop_t restartStopType)
{
    // for(int i = 0; i < len; i++)
    for(int i = (len - 1); i >= 0; i--)
    {
        /* When TX FIFO is not full, the master sends one byte */
        while(RESET == I2C_GetStatus(i2cNo, I2C_STATUS_TFNF));
        I2C_MasterSendByte(i2cNo, restartStopType, *(gTxBuffer+i));
    }
}

static void MstSendByType1(I2C_Id_t i2cNo, uint8_t * gTxBuffer, uint16_t len, I2C_RestartStop_t restartStopType)
{
    for(int i = 0; i < len; i++)
    {
        /* 当 TX FIFO 未满时，master 发送一个字节 */
        while(RESET == I2C_GetStatus(i2cNo, I2C_STATUS_TFNF));
        I2C_MasterSendByte(i2cNo, restartStopType, *(gTxBuffer+i));
    }
}

/* Write one byte of data to the destination address of the deserializer */
void Ex_MstWriteBuffer(I2C_Id_t i2cNo, uint8_t * DestAddr, uint8_t * Data)
{
    MstSendByType(i2cNo, DestAddr, DES_REG_ADDR_BYTE, I2C_RESTART_AND_STOP_DISABLE);
    MstSendByType(i2cNo, Data, 1, I2C_STOP_EN);
}

void Ex_MstWriteArray(I2C_Id_t i2cNo, uint8_t * Data)
{
    MstSendByType1(i2cNo, Data, 7u, I2C_RESTART_AND_STOP_DISABLE);
    MstSendByType1(i2cNo, Data + 7, 1, I2C_STOP_EN);
}

void Ex_MstWriteArrays(I2C_Id_t i2cNo, uint8_t * Data,uint8_t length)
{
    MstSendByType1(i2cNo, Data, (length - 1), I2C_RESTART_AND_STOP_DISABLE);
    MstSendByType1(i2cNo, Data + (length - 1), 1, I2C_STOP_EN);
}

void Ex_MstWriteSNArrays(I2C_Id_t i2cNo, uint8_t * Data)
{
    MstSendByType1(i2cNo, Data, 31, I2C_RESTART_AND_STOP_DISABLE);
    MstSendByType1(i2cNo, Data + 31, 1, I2C_STOP_EN);
}

void Ex_MstWriteHWArrays(I2C_Id_t i2cNo, uint8_t * Data)
{
    MstSendByType1(i2cNo, Data, 15, I2C_RESTART_AND_STOP_DISABLE);
    MstSendByType1(i2cNo, Data + 15, 1, I2C_STOP_EN);
}

void Ex_MstReadArray(I2C_Id_t i2cNo, uint8_t * DestAddr)
{
    MstSendByType(i2cNo, DestAddr, 1, I2C_RESTART_AND_STOP_DISABLE);
	MstRecvByType(i2cNo, 1, I2C_RESTART_AND_STOP_DISABLE);
}

/* Read one byte of data from the destination address of the deserializer */
void Ex_MstReadBuffer(I2C_Id_t i2cNo, uint8_t * DestAddr)
{
    MstSendByType(i2cNo, DestAddr, DES_REG_ADDR_BYTE, I2C_RESTART_AND_STOP_DISABLE);
    MstRecvByType(i2cNo, 1, I2C_STOP_EN);
}

static void I2C_MasterRecvCallBack(void)
{
    I2C_ClearInt(I2C0_ID, I2C_INT_RX_FULL);	
    if(u8DesFaultFlag)
    {
        u8DesFaultBuf[u8DesFaultBufSN] = I2C_ReceiveByte(I2C0_ID);
        u8DesFaultBufSN++;
    }
}

static void I2C1_MasterRecvCallBack(void)
{
    I2C_ClearInt(I2C1_ID, I2C_INT_RX_FULL);
    boI2c0IsIdle = false;	
    if(u8ModeCheckI2CFlag)
    {
        u8ModeRecvBuf[u8ModeRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8ModeRecvBufSN ++;
        if(u8ModeRecvBufSN == 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_STOP_EN);
	    }
	    else if(u8ModeRecvBufSN < 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8BKLCheckI2CFlag)
    {
        u8BKLRecvBuf[u8BKLRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8BKLRecvBufSN ++;
        if(u8BKLRecvBufSN == 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_STOP_EN);
	    }
	    else if(u8BKLRecvBufSN < 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8FaultCheckI2CFlag)
    {
        u8FaultRecvBuf[u8FaultRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8FaultRecvBufSN ++;
        if(u8FaultRecvBufSN == 13)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8FaultRecvBufSN < 13)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8HWCheckI2CFlag)
    {
        u8HWRecvBuf[u8HWRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8HWRecvBufSN ++;
        if(u8HWRecvBufSN == 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8HWRecvBufSN < 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8SWCheckI2CFlag)
    {
        u8SWRecvBuf[u8SWRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8SWRecvBufSN ++;
        if(u8SWRecvBufSN == 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8SWRecvBufSN < 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8PWCheckI2CFlag)
    {
        u8PartNumRecvBuf[u8PartNumRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8PartNumRecvBufSN ++;
        if(u8PartNumRecvBufSN == 31)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8PartNumRecvBufSN < 31)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8TempCheckI2CFlag)
    {
        u8TempRecvBuf[u8TempRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8TempRecvBufSN ++;
        if(u8TempRecvBufSN == 7)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8TempRecvBufSN < 7)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8ProCheckI2CFlag)
    {
        u8ProRecvBuf[u8ProRecvBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8ProRecvBufSN ++;
        if(u8ProRecvBufSN == 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_STOP_EN);
	    }
	    else if(u8ProRecvBufSN < 15)
	    {
	    	MstRecvByType(I2C1_ID, 1, I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if(u8BLKREGValFlag)
    {
        u8BLKREGBuf[u8BLKREGBufSN] = I2C_ReceiveByte(I2C1_ID); 
        u8BLKREGBufSN ++;
        if(u8BLKREGBufSN == 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_STOP_EN);
	    }
	    else if(u8BLKREGBufSN < 7)
	    {
            I2C_MasterReadCmd(I2C1_ID,I2C_RESTART_AND_STOP_DISABLE);
	    }
        else
        {
            /* Do nothing */
        }
    }
    else if (u8UpdateI2CFlag)
    {
        u8UpdateRecvBuf[u8UpdateRecvBufSN] = I2C_ReceiveByte(I2C1_ID);
        u8UpdateRecvBufSN++;

        if (u8UpdateRecvBufSN < u8FrameidLength - 1)
        {
            I2C_MasterReadCmd(I2C1_ID, I2C_RESTART_AND_STOP_DISABLE);
        }
        else if (u8UpdateRecvBufSN == u8FrameidLength - 1)
        {
            I2C_MasterReadCmd(I2C1_ID, I2C_STOP_EN);
        }
    }
}

static void I2C_MasterStopGeneratedCallBack(void)
{
	I2C_ClearInt(I2C0_ID, I2C_INT_STOP_DET);
    if(u8DesFaultBufSN >= 1 && u8DesFaultFlag)
    {
        u8DesFaultBufSN = 0;
        u8DesFaultFlag = 0;
    }
    
}

static void I2C1_MasterStopGeneratedCallBack(void)
{
    I2C_ClearInt(I2C1_ID, I2C_INT_STOP_DET);
    boI2c0IsIdle = true;
    if(u8ModeRecvBufSN >= 8 &&  u8ModeCheckI2CFlag)
    {
        u8ModeCheckI2CFlag = 0u;
        u8ModeRecvBufSN = 0;
    }
    else if(u8BKLRecvBufSN >= 8 && u8BKLCheckI2CFlag)
    {
        u8BKLCheckI2CFlag = 0u;
        u8BKLRecvBufSN = 0;
    }
    else if(u8FaultRecvBufSN >= 14 && u8FaultCheckI2CFlag)
    {
        u8FaultCheckI2CFlag = 0u;
        u8FaultRecvBufSN = 0;
    }
    else if (u8HWRecvBufSN >= 16 && u8HWCheckI2CFlag)
    {
        u8HWCheckI2CFlag = 0u;
        u8HWRecvBufSN = 0;
    }
    else if (u8SWRecvBufSN >= 16 && u8SWCheckI2CFlag)
    {
        u8SWCheckI2CFlag = 0u;
        u8SWRecvBufSN = 0;
    }
    else if (u8PartNumRecvBufSN >= 32 && u8PWCheckI2CFlag)
    {
        u8PWCheckI2CFlag = 0u;
        u8PartNumRecvBufSN = 0;
    }
    else if (u8TempRecvBufSN >= 8 && u8TempCheckI2CFlag)
    {
        u8TempCheckI2CFlag = 0u;
        u8TempRecvBufSN = 0;
    }
    else if (u8ProRecvBufSN >= 16 && u8ProCheckI2CFlag)
    {
        u8ProCheckI2CFlag = 0u;
        u8ProRecvBufSN = 0;
    }
    else if (u8BLKREGBufSN >= 8 && u8BLKREGValFlag)
    {
        u8BLKREGValFlag = 0u;
        u8BLKREGBufSN = 0;
    }
    else if(u8UpdateRecvBufSN == u8FrameidLength && u8UpdateI2CFlag )
    {
        memcpy(u8MstRecvAckBuf,u8UpdateRecvBuf,u8FrameidLength);//TEST 0129
        Uart_Transmit(u8UpdateRecvBuf,u8FrameidLength);
		u8UpdateI2CFlag = 0;
        u8UpdateRecvBufSN = 0;
        memset(&u8UpdateRecvBuf, 0 ,sizeof(u8UpdateRecvBuf));

         if(u8MstRecvAckBuf[0] == 0X06){
            u8Check_Flag = 1;
        }else{
            u8Check_Flag = 0;
        }
    }
    else
    {

    }
}

void I2c_Init(void)
{
    /* I2C0 master mode config */
    CLK_ModuleSrc(CLK_I2C0, CLK_SRC_OSC40M);
    CLK_SetClkDivider(CLK_I2C0, CLK_DIV_2);
    SYSCTRL_ResetModule(SYSCTRL_I2C0);
    SYSCTRL_EnableModule(SYSCTRL_I2C0);

    PORT_PinmuxConfig(PORT_A, GPIO_2, PTA2_I2C0_SDA);
    PORT_PinmuxConfig(PORT_A, GPIO_3, PTA3_I2C0_SCL);

    I2C_InstallCallBackFunc(I2C0_ID, I2C_INT_RX_FULL, I2C_MasterRecvCallBack);
    I2C_IntCmd(I2C0_ID, I2C_INT_RX_FULL, ENABLE);
    I2C_InstallCallBackFunc(I2C0_ID, I2C_INT_STOP_DET, I2C_MasterStopGeneratedCallBack);
    I2C_IntCmd(I2C0_ID, I2C_INT_STOP_DET, ENABLE);

    I2C_Disable(I2C0_ID);
    I2C_Init(I2C0_ID,&masterConfig);
    I2C_FIFOConfig(I2C0_ID, &masterFifoConfig);
    I2C_SetTargetAddr(I2C0_ID, 0x40);
    I2C_Enable(I2C0_ID);

    NVIC_EnableIRQ(I2C0_IRQn);

    /* I2C1 master mode config */
    CLK_ModuleSrc(CLK_I2C1, CLK_SRC_OSC40M);
    CLK_SetClkDivider(CLK_I2C1, CLK_DIV_2);
    SYSCTRL_ResetModule(SYSCTRL_I2C1);
    SYSCTRL_EnableModule(SYSCTRL_I2C1);

    PORT_PinmuxConfig(PORT_A, GPIO_0, PTA0_I2C1_SCL);
    PORT_PinmuxConfig(PORT_A, GPIO_1, PTA1_I2C1_SDA);

    I2C_InstallCallBackFunc(I2C1_ID, I2C_INT_RX_FULL, I2C1_MasterRecvCallBack);
    I2C_IntCmd(I2C1_ID, I2C_INT_RX_FULL, ENABLE);
    I2C_InstallCallBackFunc(I2C1_ID, I2C_INT_STOP_DET, I2C1_MasterStopGeneratedCallBack);
    I2C_IntCmd(I2C1_ID, I2C_INT_STOP_DET, ENABLE);

    I2C_Disable(I2C1_ID);
    I2C_Init(I2C1_ID,&masterConfig1);
    I2C_FIFOConfig(I2C1_ID, &masterFifoConfig);
    I2C_SetTargetAddr(I2C1_ID, 0x55u);
    I2C_Enable(I2C1_ID);

    NVIC_EnableIRQ(I2C1_IRQn);
}






