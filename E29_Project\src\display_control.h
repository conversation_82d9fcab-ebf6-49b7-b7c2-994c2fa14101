/**************************************************************************************************/
/**
 * @file      : display_control.h
 * @brief     : Display control and CAN data management module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#ifndef DISPLAY_CONTROL_H
#define DISPLAY_CONTROL_H

#include <stdint.h>

/* Vehicle display control state definitions */
typedef enum {
    MODE_INTERFACE = 0,     /* Interface switching mode */
    MODE_GEAR = 1,          /* Gear control mode */
    MODE_SPEED = 2,         /* Speed display mode */
    MODE_DOOR = 3,          /* Door status mode */
    MODE_DRIVE = 4,         /* Drive mode */
    MODE_MAX = 5
} DisplayMode_t;

/* Display state structure */
typedef struct {
    uint8_t interface_mode;     /* Interface mode: 1=default, 2-6=color themes */
    uint8_t gear_position;      /* Gear position: 1=D, 2=P, 3=N, 4=R */
    uint8_t vehicle_speed;      /* Vehicle speed: 0-255 */
    uint8_t door_status;        /* Door status: 1=6 doors open, 2=6 doors closed */
    uint8_t drive_mode;         /* Drive mode: 1=comfort, 2=drive, 3=sport, 4=eco */
} DisplayState_t;

/* Function declarations */
void Display_Control_Init(void);
void Build_CAN_Data(uint8_t *can_data);
DisplayMode_t Get_Current_Mode(void);
void Set_Current_Mode(DisplayMode_t mode);
void Next_Mode(void);

/* Display state getters */
uint8_t Display_Get_Interface_Mode(void);
uint8_t Display_Get_Gear_Position(void);
uint8_t Display_Get_Vehicle_Speed(void);
uint8_t Display_Get_Door_Status(void);
uint8_t Display_Get_Drive_Mode(void);

/* Display state setters */
void Display_Set_Interface_Mode(uint8_t mode);
void Display_Set_Gear_Position(uint8_t gear);
void Display_Set_Vehicle_Speed(uint8_t speed);
void Display_Set_Door_Status(uint8_t status);
void Display_Set_Drive_Mode(uint8_t mode);

/* Display state increment/decrement functions */
void Display_Inc_Interface_Mode(void);
void Display_Dec_Interface_Mode(void);
void Display_Inc_Gear_Position(void);
void Display_Dec_Gear_Position(void);
void Display_Inc_Vehicle_Speed(void);
void Display_Dec_Vehicle_Speed(void);
void Display_Inc_Door_Status(void);
void Display_Dec_Door_Status(void);
void Display_Inc_Drive_Mode(void);
void Display_Dec_Drive_Mode(void);

#endif /* DISPLAY_CONTROL_H */
