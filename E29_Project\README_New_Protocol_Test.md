# 安全访问与工厂模式自动启动功能

## 概述
实现了上电自动启动的安全访问协议，3秒延迟后自动开始与BusMaster进行安全访问交互，根据最终响应进入工厂模式或发送负响应。

## 协议流程

### 自动启动
- **上电后3秒延迟**: 系统自动延迟3秒
- **自动开始**: 无需按键，自动开始安全访问流程

### 第一步：请求种子
- **自动触发**: 上电3秒后自动发送
- **板子发送**: 0x5F0 → `BB CC DD EE` + 60个0x00
- **BusMaster回复**: 0x06F → `99` + 16个byte种子 + 47个0x00

### 第二步：发送密钥
- **板子收到99回复后**: 提取16字节种子，计算密钥
- **密钥计算**: 对16个种子字节进行XOR运算得到1字节密钥
- **板子发送**: 0x5F0 → `EE DD CC BB AA` + 1字节密钥 + 58个0x00
- **BusMaster回复**: 0x06F → 验证结果

### 第三步：验证结果处理
- **AA响应**: 安全访问成功，进入工厂模式
- **BB响应**: 安全访问失败，发送负响应
- **其他响应**: 发送负响应

### 工厂模式显示控制
- **进入工厂模式后**: BusMaster发送 `0x06F: 1D 11 00...`
- **显示屏响应**: 自动设置为红色显示 (interface_mode = 2)

## BusMaster测试步骤

### 1. 准备阶段
- 配置BusMaster CAN波特率
- 设置接收过滤器监听0x5F0
- 准备发送0x06F消息

### 2. 自动启动测试
1. **上电**: 连接电源和BusMaster
2. **等待3秒**: 系统自动延迟
3. **观察自动发送**: `0x5F0: BB CC DD EE 00 00 00...` (请求种子)
4. **手动回复种子**: `0x06F: 99 [16字节种子] [47个0x00]`
   - 例如: `0x06F: 99 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F 10 00 00...`

### 3. 测试密钥发送
1. 观察BusMaster接收到: `0x5F0: EE DD CC BB AA [计算的密钥] 00 00 00...`
   - 前5字节: EE DD CC BB AA
   - 第6字节: 计算的密钥 (16个种子字节XOR结果)
   - 后面58字节: 全为0x00

### 4. 测试验证结果
1. **测试成功访问**: 发送 `0x06F: AA 00 00 00 00 00 00 00`
   - 观察板子发送工厂模式进入命令
   - 手动发送: `0x06F: 1D 11 00 00 00 00 00 00`
   - 观察显示屏变红色 (interface_mode = 2)

2. **测试失败访问**: 发送 `0x06F: BB 00 00 00 00 00 00 00`
   - 观察板子回复负响应数据

### 5. 密钥计算验证
可以手动验证密钥计算：
- 种子: `01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F 10`
- 计算: `01^02^03^04^05^06^07^08^09^0A^0B^0C^0D^0E^0F^10 = 0x00`
- 期望在第6字节看到计算结果

## 状态机说明

```
TEST_STATE_IDLE ──按Button1──> TEST_STATE_STEP1_SENT
                                        │
                                   收到88回复
                                        ↓
TEST_STATE_COMPLETE <──发送响应── TEST_STATE_STEP3_SENT
                                        ↑
                                   收到99回复
                                        │
                              TEST_STATE_STEP2_SENT
```

## 数据格式

### 发送数据 (板子 → BusMaster)
- **CAN ID**: 0x5F0
- **数据长度**: 64字节 (CANFD)
- **格式**: 命令字节 + 填充0x00

### 接收数据 (BusMaster → 板子)
- **CAN ID**: 0x06F
- **数据长度**: 8字节 (经典CAN即可)
- **格式**: 响应字节 + 填充数据

## 安全访问算法说明

### 密钥计算方法
- **算法**: 简单XOR运算
- **输入**: 16字节种子数据
- **计算**: 对所有16个字节进行XOR运算
- **输出**: 1字节密钥结果

### 验证密钥计算
可以手动验证密钥计算正确性：
1. 记录BusMaster发送的16字节种子
2. 手动计算XOR结果
3. 对比板子发送的密钥值

## 注意事项

1. **工厂模式功能已注释**: 原有的工厂模式功能已被注释，Button1现在恢复界面切换功能
2. **自动启动**: 上电3秒后自动开始安全访问，无需按键
3. **状态重置**: 每次重新上电都会重新开始协议流程
4. **密钥自动计算**: 收到种子后自动计算密钥并发送
5. **调试信息**: 可以通过调试器观察`test_state`和`received_seed`变量来跟踪状态

## 恢复工厂模式功能

如需恢复原有工厂模式功能，请：
1. 注释掉`Test_New_Protocol()`调用
2. 取消注释工厂模式相关代码
3. 重新编译下载
