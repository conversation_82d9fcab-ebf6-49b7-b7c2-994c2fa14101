/**************************************************************************************************/
/**
 * @file      : factory_mode.c
 * @brief     : Factory mode management module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "factory_mode.h"
#include "can.h"
#include "display_control.h"

/* Factory mode state variable */
static FactoryModeState_t factory_mode_state = FACTORY_MODE_NORMAL;

/* Factory mode command data */
static uint8_t factory_enter_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFE,  /* Enter factory mode command */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00        
};

static uint8_t factory_exit_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFF,  /* Exit factory mode command */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00         
};

/**
 * @brief Initialize factory mode module
 */
void Factory_Mode_Init(void)
{
    factory_mode_state = FACTORY_MODE_NORMAL;
}

/**
 * @brief Enter factory mode
 */
void Enter_Factory_Mode(void)
{
    CAN_Send_FactoryMode_Msg(0x5F0, factory_enter_cmd);
}

/**
 * @brief Exit factory mode
 */
void Exit_Factory_Mode(void)
{
    CAN_Send_FactoryMode_Msg(0x5F0, factory_exit_cmd);
}

/**
 * @brief Handle factory mode response and display control
 * @param response_byte1: First byte of response
 */
void Handle_Factory_Mode_Response(uint8_t response_byte1)
{
    if(response_byte1 == 0x11) {
        /* Factory mode confirmed: Set display to red (interface_mode = 2) */
        Display_Set_Interface_Mode(2);
        CAN_Send_FactoryMode_Msg(0x5F0, factory_enter_cmd);
        factory_mode_state = FACTORY_MODE_ACTIVE;
    }
    else if(response_byte1 == 0x22) {
        /* Exit factory mode */
        CAN_Send_FactoryMode_Msg(0x5F0, factory_exit_cmd);
        factory_mode_state = FACTORY_MODE_NORMAL;
    }
}

/**
 * @brief Get current factory mode state
 * @return Current factory mode state
 */
FactoryModeState_t Get_Factory_Mode_State(void)
{
    return factory_mode_state;
}
