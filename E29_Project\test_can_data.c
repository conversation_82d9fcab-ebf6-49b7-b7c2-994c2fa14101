/**************************************************************************************************/
/**
 * @file      : test_can_data.c
 * @brief     : 测试CAN数据构建功能
 * @version   : V1.0.0
 * @date      : 2024
 * <AUTHOR> 
 *
 * @note      : 这是一个测试文件，用于验证CAN数据包构建的正确性
 **************************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <string.h>

// 模拟显示状态结构体
typedef struct {
    uint8_t interface_mode;     // 界面模式: 1=默认仪表, 2-6=红绿蓝白黑纯色
    uint8_t gear_position;      // 挡位: 1=D, 2=P, 3=N, 4=R
    uint8_t vehicle_speed;      // 车速: 0-255
    uint8_t door_status;        // 门状态: 1=6门开, 2=6门关
    uint8_t drive_mode;         // 驾驶模式: 1=舒适, 2=驾驶, 3=运动, 4=节能
} DisplayState_t;

// 测试用显示状态
DisplayState_t test_display_state = {
    .interface_mode = 1,    // 默认仪表主题界面
    .gear_position = 2,     // 默认P档
    .vehicle_speed = 0,     // 默认车速0
    .door_status = 2,       // 默认6门关
    .drive_mode = 1         // 默认舒适模式
};

// 构建CAN数据包 (测试版本)
void Test_Build_CAN_Data(uint8_t *can_data, DisplayState_t *state)
{
    // 清空数据包
    for(int i = 0; i < 64; i++) {
        can_data[i] = 0x00;
    }
    
    // Byte0: 主观功能 (固定值0x0D)
    can_data[0] = 0x0D;
    
    // Byte1: 界面切换 (1-6)
    can_data[1] = state->interface_mode;
    
    // Byte2: 挡位显示 (1-4: D,P,N,R)
    can_data[2] = state->gear_position;
    
    // Byte3: 车速显示 (0-255, P档不显示车速)
    if(state->gear_position == 2) { // P档
        can_data[3] = 0;
    } else {
        can_data[3] = state->vehicle_speed;
    }
    
    // Byte4: 门开显示 (1-2: 6门开/6门关)
    can_data[4] = state->door_status;
    
    // Byte5: 驾驶模式 (1-4: 舒适,驾驶,运动,节能)
    can_data[5] = state->drive_mode;
    
    // Byte6-Byte63: 固定发送58个0x00 (已在清空时设置)
}

// 打印CAN数据包
void Print_CAN_Data(uint8_t *can_data, const char* test_name)
{
    printf("\n=== %s ===\n", test_name);
    printf("CAN ID: 0x5F0\n");
    printf("Data: ");
    for(int i = 0; i < 64; i++) {
        printf("%02X ", can_data[i]);
        if((i + 1) % 16 == 0) printf("\n      ");
    }
    printf("\n");
    
    printf("解析:\n");
    printf("  Byte0 (主观功能): 0x%02X\n", can_data[0]);
    printf("  Byte1 (界面模式): %d\n", can_data[1]);
    printf("  Byte2 (挡位): %d\n", can_data[2]);
    printf("  Byte3 (车速): %d\n", can_data[3]);
    printf("  Byte4 (门状态): %d\n", can_data[4]);
    printf("  Byte5 (驾驶模式): %d\n", can_data[5]);
}

// 测试不同场景
void Run_Tests(void)
{
    uint8_t can_data[64];
    
    // 测试1: 初始状态
    Test_Build_CAN_Data(can_data, &test_display_state);
    Print_CAN_Data(can_data, "初始状态测试");
    
    // 测试2: D档高速行驶
    test_display_state.gear_position = 1; // D档
    test_display_state.vehicle_speed = 120; // 120km/h
    test_display_state.interface_mode = 3; // 绿色界面
    test_display_state.drive_mode = 3; // 运动模式
    Test_Build_CAN_Data(can_data, &test_display_state);
    Print_CAN_Data(can_data, "D档高速行驶测试");
    
    // 测试3: P档停车
    test_display_state.gear_position = 2; // P档
    test_display_state.vehicle_speed = 50; // 设置车速，但P档应该显示0
    test_display_state.door_status = 1; // 6门开
    test_display_state.drive_mode = 1; // 舒适模式
    Test_Build_CAN_Data(can_data, &test_display_state);
    Print_CAN_Data(can_data, "P档停车测试 (车速应为0)");
    
    // 测试4: R档倒车
    test_display_state.gear_position = 4; // R档
    test_display_state.vehicle_speed = 5; // 低速倒车
    test_display_state.interface_mode = 6; // 黑色界面
    test_display_state.drive_mode = 4; // 节能模式
    Test_Build_CAN_Data(can_data, &test_display_state);
    Print_CAN_Data(can_data, "R档倒车测试");
}

int main(void)
{
    printf("汽车电子显示屏CAN数据包测试程序\n");
    printf("=====================================\n");
    
    Run_Tests();
    
    printf("\n测试完成!\n");
    return 0;
}
