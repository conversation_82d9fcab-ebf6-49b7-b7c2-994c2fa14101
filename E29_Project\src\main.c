/**************************************************************************************************/
/**
 * @file      : main.c
 * @brief     : 
 * @version   : V1.8.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @note      : This example contains sample code for customer evaluation purpose only. It is not
 * part of the production code deliverables. The example code is only tested under defined
 * environment with related context of the whole example project. Therefore, it is not guaranteed
 * that the example always works under user environment due to the diversity of hardware and
 * software environment. Do not use pieces copy of the example code without prior and separate
 * verification and validation in user environment.
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "Z20K11xM_drv.h"
#include "Z20K11xM_clock.h"
#include "Z20K11xM_sysctrl.h"
#include "Z20K11xM_wdog.h"
#include "Z20K11xM_gpio.h"
#include "Z20K11xM_uart.h"
#include "Z20K118M.h" 


#include "i2c.h"
#include "uart.h"
#include "common_func.h"
#include "stim.h"
#include "wdog.h"
#include "pwm.h"
#include "diag.h"
#include "SysTick.h"
#include "Scheduler.h"
#include "can.h"

// #define WDOG_EN		0


typedef enum {
    BUTTON_NONE = 0,
    BUTTON_1_PRESSED = 1,    
    BUTTON_2_PRESSED = 2,     
    BUTTON_3_PRESSED = 3     
} ButtonState_t;

static volatile ButtonState_t button_pressed = BUTTON_NONE;

/* Vehicle display control state definitions */
typedef enum {
    MODE_INTERFACE = 0,     /* Interface switching mode */
    MODE_GEAR = 1,          /* Gear control mode */
    MODE_SPEED = 2,         /* Speed display mode */
    MODE_DOOR = 3,          /* Door status mode */
    MODE_DRIVE = 4,         /* Drive mode */
    MODE_MAX = 5
} DisplayMode_t;

/* Display state structure */
typedef struct {
    uint8_t interface_mode;     /* Interface mode: 1=default, 2-6=color themes */
    uint8_t gear_position;      /* Gear position: 1=D, 2=P, 3=N, 4=R */
    uint8_t vehicle_speed;      /* Vehicle speed: 0-255 */
    uint8_t door_status;        /* Door status: 1=6 doors open, 2=6 doors closed */
    uint8_t drive_mode;         /* Drive mode: 1=comfort, 2=drive, 3=sport, 4=eco */
} DisplayState_t;


static DisplayState_t display_state = {
    .interface_mode = 1,    /* Default dashboard theme */
    .gear_position = 2,     /* Default P gear */
    .vehicle_speed = 0,     /* Default speed 0 */
    .door_status = 2,       /* Default 6 doors closed */
    .drive_mode = 1         /* Default comfort mode */
};

/* Current operation mode */
static DisplayMode_t current_mode = MODE_INTERFACE;

typedef enum {
    FACTORY_MODE_NORMAL = 0,    /* Normal mode */
    FACTORY_MODE_ACTIVE = 1     /* Factory mode active */
} FactoryModeState_t;

static FactoryModeState_t factory_mode_state = FACTORY_MODE_NORMAL;

/* Security access */
typedef enum {
    TEST_STATE_IDLE = 0,        /* Idle state */
    TEST_STATE_STEP1_SENT = 1,  /* Step 1 sent: AA BB CC DD EE */
    TEST_STATE_STEP2_SENT = 2,  /* Step 2 sent: EE DD CC BB AA */
    TEST_STATE_COMPLETE = 3     /* Test complete */
} TestProtocolState_t;

static TestProtocolState_t test_state = TEST_STATE_IDLE;

// Security access state variables
volatile uint8_t received_seed[16] = {0};  // Store received 16-byte seed

/* Factory mode command data */
uint8_t factory_enter_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFE,  /* Enter factory mode command */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

uint8_t factory_exit_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFF,  /* Exit factory mode command */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

// New security access protocol data
uint8_t security_request_seed[64] = {
    0xAA,0xBB, 0xCC, 0xDD, 0xEE,  // Request seed command (5bytes)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00         
};

uint8_t security_send_key[64] = {
    0xEE, 0xDD, 0xCC, 0xBB, 0xAA,  /* Send key command (5 bytes) */
    0x00,                          /* Calculated seed (1 byte) - will be calculated */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00              /* 8 zeros, total 58 zeros */
};


uint8_t negative_response[64] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00                                        /* 4 zeros, total 64 zeros */
};

// Calculate simple seed for security access
uint8_t Calculate_Security_Seed(const uint8_t *seed_data)
{
    // Simple calculation: XOR all 16 bytes of received seed
    uint8_t result = 0;
    for(int i = 0; i < 16; i++) {
        result ^= seed_data[i];
    }
    return result;
}


// Prepare security key command with calculated seed
void Prepare_Security_Key_Command(void)
{
    // Calculate seed from received data
    uint8_t calculated_seed = Calculate_Security_Seed(received_seed);

    // Fill the calculated seed into position 5 (after EE DD CC BB AA)
    security_send_key[5] = calculated_seed;
}

void Enter_Security_Mode(void)
{
    // Send request seed command: AA BB CC DD EE
    CAN_Send_FactoryMode_Msg(0x5F0, security_request_seed);
    test_state = TEST_STATE_STEP1_SENT;
}

void Enter_Factory_Mode(void)
{
    CAN_Send_FactoryMode_Msg(0x5F0, factory_enter_cmd);
}

void Exit_Factory_Mode(void)
{

    CAN_Send_FactoryMode_Msg(0x5F0, factory_exit_cmd);
}

// Handle security access protocol response
void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len)
{
    switch(test_state) {
        case TEST_STATE_STEP1_SENT:
            // Expect: 99 + 16-byte seed + 47 zeros
            if(response_data[0] == 0x99) {
                // Store received 16-byte seed (bytes 1-16)
                for(int i = 0; i < 16; i++) {
                    received_seed[i] = response_data[i + 1];
                }
                // Prepare and send security key
                Prepare_Security_Key_Command();
                CAN_Send_FactoryMode_Msg(0x5F0, security_send_key);
                test_state = TEST_STATE_STEP2_SENT;
            }
            break;

        case TEST_STATE_STEP2_SENT:
            // Handle security access verification result
            if(response_data[0] == 0xAA) {
                // AA response: Security access successful, enter factory mode
                Enter_Factory_Mode();
                test_state = TEST_STATE_COMPLETE;
            } else if(response_data[0] == 0xBB) {
                // BB response: Security access failed, send negative response
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = TEST_STATE_COMPLETE;
            } else {
                // Other responses: Send negative response
                CAN_Send_FactoryMode_Msg(0x5F0, negative_response);
                test_state = TEST_STATE_COMPLETE;
            }
            break;

        default:
            break;
    }
}

// Handle factory mode response and display control
void Handle_Factory_Mode_Response(uint8_t response_byte1)
{
    if(response_byte1 == 0x11) {
        // Factory mode confirmed: Set display to red (interface_mode = 2)
        display_state.interface_mode = 2;
        CAN_Send_FactoryMode_Msg(0x5F0, factory_enter_cmd);
        factory_mode_state = FACTORY_MODE_ACTIVE;
    }
    else if(response_byte1 == 0x22) {
        // Exit factory mode
        CAN_Send_FactoryMode_Msg(0x5F0, factory_exit_cmd);
        factory_mode_state = FACTORY_MODE_NORMAL;
    }
}

__STATIC_INLINE void SysTick_Init(void)
{
  SysTick->LOAD  = 0x00FFFFFEu;                         /* set reload register */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
}

uint8_t u8SW2 = 0u;
uint8_t u8SW1 = 0u;
uint8_t u8SW3 = 0u;

void PortCInt(PORT_ID_t portId, PORT_GPIONO_t gpioNo);

void GPIOIntInit(void)
{
    PORT_InstallCallBackFunc(PortCInt);

    /* Clear interrupt flag*/
    PORT_ClearPinInt(PORT_D, GPIO_2);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_D, GPIO_2, PTD2_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_D, GPIO_2, PORT_PULL_DOWN);
    /* input direction for PTD2 */
    GPIO_SetPinDir(PORT_D, GPIO_2, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_D, GPIO_2, PORT_ISF_INT_RISING_EDGE);

    /* Clear interrupt flag for PTC2 - Button 2 */
    PORT_ClearPinInt(PORT_C, GPIO_2);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_C, GPIO_2, PTC2_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_C, GPIO_2, PORT_PULL_DOWN);
    /* input direction for PTC2 */
    GPIO_SetPinDir(PORT_C, GPIO_2, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_C, GPIO_2, PORT_ISF_INT_RISING_EDGE);

    /* Clear interrupt flag for PTC3 - Button 3 */
    PORT_ClearPinInt(PORT_C, GPIO_3);
    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_C, GPIO_3, PTC3_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_C, GPIO_3, PORT_PULL_DOWN);
    /* input direction for PTC3 */
    GPIO_SetPinDir(PORT_C, GPIO_3, GPIO_INPUT);
    /* port interrupt config*/
    PORT_PinIntConfig(PORT_C, GPIO_3, PORT_ISF_INT_RISING_EDGE);

    NVIC_SetPriority(PORTABC_IRQn, 1u);
    NVIC_SetPriority(PORTDE_IRQn, 1u);
    /* enable NVIC IRQ*/
    NVIC_EnableIRQ(PORTABC_IRQn);
    NVIC_EnableIRQ(PORTDE_IRQn);
}




void Build_CAN_Data(uint8_t *can_data)
{
    for(int i = 0; i < 64; i++) {
        can_data[i] = 0x00;
    }
    /* Byte0: Main function (fixed value 0x0D) */
    can_data[0] = 0x0D;

    /* Byte1: Interface switching (1-6) */
    can_data[1] = display_state.interface_mode;

    /* Byte2: Gear display (1-4: D,P,N,R) */
    can_data[2] = display_state.gear_position;

    /* Byte3: Speed display (0-255, no speed in P gear) */
    if(display_state.gear_position == 2) { /* P gear */
        can_data[3] = 0;
    } else {
        can_data[3] = display_state.vehicle_speed;
    }

    /* Byte4: Door status (1-2: 6 doors open/closed) */
    can_data[4] = display_state.door_status;

    /* Byte5: Drive mode (1-4: comfort,drive,sport,eco) */
    can_data[5] = display_state.drive_mode;
}

void Process_Button_CAN(void)
{
    uint8_t can_data[64] = {0};

    if (button_pressed == BUTTON_1_PRESSED)
    {
        
        current_mode++;
        if(current_mode >= MODE_MAX) {
            current_mode = MODE_INTERFACE;
        }

        /* Build and send CAN data */
        Build_CAN_Data(can_data);
        CAN_Send_Button1_Msg(0x5F0, can_data);

    }
    else if (button_pressed == BUTTON_2_PRESSED)
    {
        /* Button2: Increase current mode value */
        switch(current_mode) {
            case MODE_INTERFACE:
                display_state.interface_mode++;
                if(display_state.interface_mode > 6) {
                    display_state.interface_mode = 1;
                }
                break;
            case MODE_GEAR:
                display_state.gear_position++;
                if(display_state.gear_position > 4) {
                    display_state.gear_position = 4;
                }
                break;
            case MODE_SPEED:
                if(display_state.vehicle_speed < 255) {
                    display_state.vehicle_speed += 1; 
                    if(display_state.vehicle_speed > 255) {
                        display_state.vehicle_speed = 255;
                    }
                }
                break;
            case MODE_DOOR:
                display_state.door_status++;
                if(display_state.door_status > 2) {
                    display_state.door_status = 1;
                }
                break;
            case MODE_DRIVE:
                display_state.drive_mode++;
                if(display_state.drive_mode > 4) {
                    display_state.drive_mode = 4;
                }
                break;
        }

        Build_CAN_Data(can_data);
        CAN_Send_Button2_Msg(0x5F0, can_data);
    }
    else if (button_pressed == BUTTON_3_PRESSED)
    {
        /* Button3: Decrease current mode value */
        switch(current_mode) {
            case MODE_INTERFACE:
                if(display_state.interface_mode > 1) {
                    display_state.interface_mode--;
                } else {
                    display_state.interface_mode = 6;
                }
                break;
            case MODE_GEAR:
                if(display_state.gear_position > 1) {
                    display_state.gear_position--;
                } else {
                    display_state.gear_position = 1;
                }
                break;
            case MODE_SPEED:
                if(display_state.vehicle_speed >= 1) {
                    display_state.vehicle_speed -= 1; 
                } else {
                    display_state.vehicle_speed = 0;
                }
                break;
            case MODE_DOOR:
                if(display_state.door_status > 1) {
                    display_state.door_status--;
                } else {
                    display_state.door_status = 2;
                }
                break;
            case MODE_DRIVE:
                if(display_state.drive_mode > 1) {
                    display_state.drive_mode--;
                } else {
                    display_state.drive_mode = 1;
                }
                break;
        }

        /* Build and send CAN data */
        Build_CAN_Data(can_data);
        CAN_Send_Button3_Msg(0x5F0, can_data);
    }

    delay(100000);
    button_pressed = BUTTON_NONE;
}


void PortCInt(PORT_ID_t portId, PORT_GPIONO_t gpioNo)
{
    switch (portId)
    {
    case PORT_C:
        if (gpioNo == GPIO_2)
        {
            button_pressed = BUTTON_2_PRESSED;  
        }
        else if(gpioNo == GPIO_3)
        {
            
           button_pressed = BUTTON_3_PRESSED;
        }
        break;
    case PORT_D:
        if (gpioNo == GPIO_2)
        {
            
            button_pressed = BUTTON_1_PRESSED;  
        }
        break;
    default:
        break;
    }
}

void System_Init(void)
{
    /* Disable wdog */
    SYSCTRL_EnableModule(SYSCTRL_WDOG);
    WDOG_Disable();

    /* Enable OSC40M clock*/
    CLK_OSC40MEnable2(CLK_OSC_FREQ_MODE_HIGH, ENABLE, CLK_OSC_XTAL);
    /* Select OSC40M as system clock*/
    CLK_SysClkSrc(CLK_SYS_FIRC64M);         // zx review , CLK_SYS_OSC40M -> CLK_SYS_FIRC64M
    CLK_SetClkDivider(CLK_CORE, CLK_DIV_1); // zx review
    CLK_SetClkDivider(CLK_BUS, CLK_DIV_2);  // zx review
    CLK_SetClkDivider(CLK_SLOW, CLK_DIV_8); // zx review
}

void Common_Init(void)
{
    CLK_ModuleSrc(CLK_PORTA, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTA);

    CLK_ModuleSrc(CLK_PORTB, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTB);

    CLK_ModuleSrc(CLK_PORTC, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTC);

    CLK_ModuleSrc(CLK_PORTD, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTD);

    CLK_ModuleSrc(CLK_PORTE, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTE);

    /* enable GPIO module*/
    SYSCTRL_EnableModule(SYSCTRL_GPIO);
}

void Peripheral_Init(void)
{
    I2c_Init();
    Uart0_Init();
    SysTickInit();
    CANConfig_Init();    //v1.0.3
}

int main()
{
    /* system init */
    System_Init();
    /* common module init */
    Common_Init();


    /* pull up deserialuzer power v1.0.1 */
    /* NOTE: PTA11 and PTA10 are reserved for CAN TX/RX - DO NOT configure as GPIO */
    // PORT_PinmuxConfig(PORT_A, GPIO_11, PTA11_GPIO);  // RESERVED FOR CAN TX
    // GPIO_SetPinDir(PORT_A, GPIO_11, GPIO_OUTPUT);
    // GPIO_SetPinOutput(PORT_A, GPIO_11);

    // /* pull up deserialuzer power */
    PORT_PinmuxConfig(PORT_A, GPIO_13, PTA13_GPIO);
    GPIO_SetPinDir(PORT_A, GPIO_13, GPIO_OUTPUT);
    GPIO_SetPinOutput(PORT_A, GPIO_13);

    PORT_PinmuxConfig(PORT_A, GPIO_12, PTA12_GPIO);
    GPIO_SetPinDir(PORT_A, GPIO_12, GPIO_OUTPUT);
    GPIO_SetPinOutput(PORT_A, GPIO_12);

    /* set pin as GPIO*/
    PORT_PinmuxConfig(PORT_C, GPIO_5, PTC5_GPIO);
    /* an initial voltage */
    PORT_PullConfig(PORT_C, GPIO_5, PORT_PULL_DOWN);

    /* input direction for PTC5 */
    GPIO_SetPinDir(PORT_C, GPIO_5, GPIO_INPUT);

    /* Initialize GPIO interrupts for buttons */
    GPIOIntInit();

    Peripheral_Init();

    /* Wait for CAN initialization to complete */
    for(volatile uint32_t i = 0; i < 1000000; i++);

    /* 3 second delay after power-on, then start security access automatically */
    for(volatile uint32_t i = 0; i < 30000000; i++);  /* 3s delay */

    Enter_Security_Mode();

    while(1)
    {
        if(button_pressed != BUTTON_NONE)
        {
            Process_Button_CAN();
        }
    }
}
