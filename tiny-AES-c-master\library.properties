name=tiny-AES-c
version=1.0.0
author=kokke
maintainer=kokke
sentence=A small and portable implementation of the AES ECB, CTR and CBC encryption algorithms written in C.
paragraph=You can override the default key-size of 128 bit with 192 or 256 bit by defining the symbols AES192 or AES256 in aes.h. The API is very simple. No padding is provided so for CBC and ECB all buffers should be multiples of 16 bytes.
category=Data Processing
url=https://github.com/kokke/tiny-AES-c
repository=https://github.com/kokke/tiny-AES-c.git
architectures=*
includes=aes.h
