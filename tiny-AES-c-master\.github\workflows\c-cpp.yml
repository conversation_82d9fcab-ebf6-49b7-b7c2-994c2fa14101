name: CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  schedule:
    # Run CI nightly (midnight)
    - cron: "0 0 * * *"

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: make clean
      run: make clean
    - name: make
      run: make
    - name: make test
      run: make test
