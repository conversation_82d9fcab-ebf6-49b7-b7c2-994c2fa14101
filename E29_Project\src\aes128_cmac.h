/**************************************************************************************************/
/**
 * @file      : aes128_cmac.h
 * @brief     : AES128-CMAC implementation using tiny-AES-c library
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @note      : This implementation uses the tiny-AES-c library for AES128 operations
 *              and implements CMAC (Cipher-based Message Authentication Code) on top
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#ifndef AES128_CMAC_H
#define AES128_CMAC_H

#include <stdint.h>
#include <stddef.h>

/* AES128-CMAC constants */
#define AES128_BLOCK_SIZE   16
#define AES128_KEY_SIZE     16
#define AES128_CMAC_SIZE    16

/* Function declarations */

/**
 * @brief Calculate AES128-CMAC for a message
 * @param key: 16-byte AES128 key
 * @param message: Input message data
 * @param message_len: Length of message in bytes
 * @param mac: Output buffer for 16-byte MAC
 */
void AES128_CMAC_Calculate(const uint8_t *key, const uint8_t *message, size_t message_len, uint8_t *mac);

/**
 * @brief Generate CMAC subkeys K1 and K2 from AES key
 * @param key: 16-byte AES128 key
 * @param k1: Output buffer for subkey K1 (16 bytes)
 * @param k2: Output buffer for subkey K2 (16 bytes)
 */
void AES128_CMAC_Generate_Subkeys(const uint8_t *key, uint8_t *k1, uint8_t *k2);

/**
 * @brief XOR two 16-byte blocks
 * @param a: First block
 * @param b: Second block  
 * @param result: Output block (a XOR b)
 */
void AES128_CMAC_XOR_Block(const uint8_t *a, const uint8_t *b, uint8_t *result);

/**
 * @brief Left shift a 16-byte block by 1 bit
 * @param input: Input block
 * @param output: Output block (input << 1)
 */
void AES128_CMAC_Left_Shift(const uint8_t *input, uint8_t *output);

/**
 * @brief Test function for AES128-CMAC implementation
 * @return 0 if test passes, non-zero if test fails
 */
int AES128_CMAC_Test(void);

#endif /* AES128_CMAC_H */
