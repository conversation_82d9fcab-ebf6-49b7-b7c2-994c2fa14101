# 汽车电子显示屏CAN控制系统

## 概述
本系统实现了通过按键控制汽车电子显示屏的CAN报文发送功能，支持界面切换、挡位控制、车速显示、门开显示和驾驶模式切换。

## CAN报文格式
- **CAN ID**: 0x5F0
- **数据长度**: 64字节 (CANFD)
- **Byte0**: 主观功能 (固定值0x0D)
- **Byte1**: 界面切换 (1-6)
- **Byte2**: 挡位显示 (1-4: D,P,N,R)
- **Byte3**: 车速显示 (0-255, P档时为0)
- **Byte4**: 门开显示 (1-2: 6门开/6门关)
- **Byte5**: 驾驶模式 (1-4: 舒适,驾驶,运动,节能)
- **Byte6-63**: 固定发送58个0x00

## 按键功能

### Button1 (PTD2) - 工厂模式控制/模式切换
**工厂模式控制**：
- 正常模式下：按下进入工厂模式（发送进入命令到0x5F0，等待0x06F响应）
- 工厂模式下：按下退出工厂模式（发送退出命令到0x5F0，等待0x06F响应）

**模式切换**（仅在工厂模式转换过程中）：
按下Button1可以在以下模式间循环切换：
1. 界面切换模式
2. 挡位控制模式
3. 车速显示模式
4. 门开显示模式
5. 驾驶模式

### Button2 (PTC2) - 数值增加
在当前模式下增加对应的数值：
- **界面模式**: 1→2→3→4→5→6→1 (默认仪表→红→绿→蓝→白→黑→默认仪表)
- **挡位模式**: 1→2→3→4→1 (D→P→N→R→D)
- **车速模式**: 当前车速+10 (最大255)
- **门开模式**: 1→2→1 (6门开→6门关→6门开)
- **驾驶模式**: 1→2→3→4→1 (舒适→驾驶→运动→节能→舒适)

### Button3 (PTC3) - 数值减少
在当前模式下减少对应的数值：
- **界面模式**: 6→5→4→3→2→1→6 (黑→白→蓝→绿→红→默认仪表→黑)
- **挡位模式**: 4→3→2→1→4 (R→N→P→D→R)
- **车速模式**: 当前车速-10 (最小0)
- **门开模式**: 2→1→2 (6门关→6门开→6门关)
- **驾驶模式**: 4→3→2→1→4 (节能→运动→驾驶→舒适→节能)

## 工厂模式协议

### 进入工厂模式流程
1. **按Button1** → 板子发送0x5F0命令: `0x1D 0x29 0x33 0x88 0xFE` + 59个0x00
2. **BusMaster回复** → 发送0x06F响应: `0x1D 0x11` + 62个0x00
3. **板子收到0x06F** → 再次发送0x5F0命令: `0x1D 0x29 0x33 0x88 0xFE` + 59个0x00

### 退出工厂模式流程
1. **按Button1** → 板子发送0x5F0命令: `0x1D 0x29 0x33 0x88 0xFF` + 59个0x00
2. **BusMaster回复** → 发送0x06F响应: `0x1D 0x22` + 62个0x00
3. **板子收到0x06F** → 再次发送0x5F0命令: `0x1D 0x29 0x33 0x88 0xFF` + 59个0x00

## BusMaster测试配置

### 接收配置 (监听板子发送的消息)
- **CAN ID**: 0x5F0
- **数据格式**: 64字节CANFD
- **监听内容**: 工厂模式进入/退出命令

### 发送配置 (模拟显示屏回复)
1. **进入工厂模式回复**:
   - CAN ID: 0x06F
   - 数据: `1D 11 00 00 00 00 00 00` (8字节即可)

2. **退出工厂模式回复**:
   - CAN ID: 0x06F
   - 数据: `1D 22 00 00 00 00 00 00` (8字节即可)

### 测试步骤
1. 启动BusMaster，配置CAN波特率
2. 按板子Button1 → 观察0x5F0消息 (进入命令)
3. 手动发送0x06F回复 (1D 11) → 观察板子再次发送0x5F0
4. 再按Button1 → 观察0x5F0消息 (退出命令)
5. 手动发送0x06F回复 (1D 22) → 观察板子再次发送0x5F0

## 特殊逻辑
1. **P档车速**: 当挡位为P档(2)时，车速自动设置为0
2. **车速调节**: 每次按键调节1km/h，范围0-255
3. **循环切换**: 所有模式都支持循环切换
4. **工厂模式状态**: 系统会跟踪工厂模式状态（正常/进入中/激活/退出中）

## 初始状态
- 界面模式: 1 (默认仪表主题界面)
- 挡位: 2 (P档)
- 车速: 0
- 门状态: 2 (6门关)
- 驾驶模式: 1 (舒适模式)

## 硬件连接
- Button1: PTD2 (上拉输入，上升沿触发)
- Button2: PTC2 (上拉输入，上升沿触发)
- Button3: PTC3 (上拉输入，上升沿触发)
- CAN TX: PTA11
- CAN RX: PTA10

## 编译和使用
1. 使用Keil MDK打开项目文件 `E29_Project/KeilProject/E29_mcu.uvprojx`
2. 编译项目
3. 下载到Z20K118M开发板
4. 连接CAN总线
5. 按下按键测试功能

## 调试信息
系统会在启动时发送初始状态的CAN报文，每次按键操作后也会发送相应的CAN报文。可以使用CAN分析仪监控报文内容。

## 注意事项
1. 确保CAN总线正确连接和终端电阻配置
2. 按键有防抖延时，连续按键需要间隔100ms以上
3. 系统使用CANFD格式，波特率配置在can.c中
4. 如需修改CAN ID或数据格式，请修改相应的宏定义和数据结构
