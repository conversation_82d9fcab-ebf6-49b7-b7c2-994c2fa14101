/**************************************************************************************************/
/**
 * @file     can.h
 * @brief    
 * @version  V1.0.0
 * @date     June-2024
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#ifndef CAN_H
#define CAN_H

#include "Z20K11xM_drv.h"
#include "Z20K11xM_clock.h"
#include "Z20K11xM_sysctrl.h"
#include "Z20K11xM_wdog.h"
#include "Z20K11xM_gpio.h"
#include "Z20K11xM_can.h"
#include "Z20K118M.h" 

#define  MODEON         0x00
#define  MODESTANDBY    0x01
#define  MODEOFF        0x02

/* CANFD related definitions */
#define CAN_FD_MAX_DATA_LENGTH    64u    /* Maximum data length for CANFD */
#define CAN_STD_MAX_DATA_LENGTH   8u     /* Maximum data length for standard CAN */

extern uint8_t au8CanData[64u];
extern uint8_t u8CanReadData;
extern uint8_t u8CanVersion;
extern uint8_t u8CANWriteTask;
extern uint8_t u8CANReadTask;
extern uint8_t u8CanDesTask;
extern uint8_t u8CanBLKTask;
extern uint8_t u8SwitchColorTask;
extern uint8_t u8CANWriteHWTask;
extern uint8_t u8CANWriteSNTask;

extern uint8_t au8CanDataHW[16u];
extern uint8_t au8CanDataSN[32u];

extern uint8_t u8InitFlag;

extern void CANConfig_Init(void);
extern void CAN_Send_Msg(uint32_t msgId, const uint8_t *msgData);
extern void CAN_Send_Button1_Msg(uint32_t msgId, const uint8_t *msgData);
extern void CAN_Send_Button2_Msg(uint32_t msgId, const uint8_t *msgData);
extern void CAN_Send_Button3_Msg(uint32_t msgId, const uint8_t *msgData);
extern void CAN_Send_FactoryMode_Msg(uint32_t msgId, const uint8_t *msgData);



#endif /* CAN_H */

   