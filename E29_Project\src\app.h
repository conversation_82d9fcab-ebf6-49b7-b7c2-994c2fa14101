#ifndef APP_H
#define APP_H

#include "Z20K11xM_drv.h"
#include "Z20K11xM_i2c.h"
#include "string.h"
#include "stdbool.h"
#include "uart.h"

#define MCU_IIC         0x55u
#define DEV_SER         0x40u
#define DEV_DES         0x48u

#define TABLE_MAX		34

extern uint8_t u8InitFlag;
extern uint8_t u8SW2;
extern uint8_t u8SW1;
extern uint8_t u8SW3;

extern uint8_t u8HWRecvBuf[16];
extern uint8_t u8SWRecvBuf[16];
extern uint8_t u8PartNumRecvBuf[32];
extern uint8_t u8TempRecvBuf[8];
extern uint8_t u8ModeRecvBuf[8];
extern uint8_t u8BKLRecvBuf[8];
extern uint8_t u8FaultRecvBuf[16];
extern uint8_t u8ProRecvBuf[16];
extern uint8_t u8BLKREGBuf[8];
extern uint8_t u8DesFaultBuf[8];

extern uint8_t u8UartRecvIndex;

//MCU Update
#define UPDATE_DATA_START_ADDR  0x00010000
#define UPDATE_DATA_SIZE        0xC000 + 0xC000

extern bool boTimer_200MsFlag;
extern bool boTimer_2MsFlag;
extern bool boTimer_100MsFlag;
extern bool boTimer_5MsFlag;

//TEST FOR Download data
enum ACK
{
	Flashing_OK,
	Check_Failed,
	File_Error,
	Flashing_Failed,
	Seq_Error,
};

typedef enum 
{
	Update_Success,
	Updating,
	Update_Failed,
	Update_Complete,
}Status;

typedef enum
{
	Timer_1ms,
	Timer_2ms,
	Timer_5ms,
	Timer_100ms,
	Timer_200ms,
}eUpdateTiemr;

enum result
{
	OK,
	Failed,
};
//TEST FOR Download data

typedef struct
{
	uint8_t Temp;
	uint32_t Resistance;
}ResistanceTransition;

extern void App_vMain(void);
extern void App_vSwitchColor(void);
extern void App_vButtonColor(void);

extern void App_vSendCan(void);
extern void App_vUpdateFraneSend(void);

typedef enum
{
	Mode_Idle,
	Mode_CanWriteTask,
	Mode_CanReadTask,
	Mode_SwitchColor,
	Mode_ReadHWSWPW,
	Mode_CycleReadTemp,
	Mode_ButtonBKL,
	Mode_UartWrite,
	Mode_UartRead,
	Mode_CanEEClear,
	Mode_UartUpdate,
	Mode_UartDes,
	Mode_CanBLKFault,
	Mode_CanWriteHW,
	Mode_CanWriteSN,
} I2CWorkState;

typedef struct I2cSendData
{
    uint8_t   u8DstAddr;
    uint16_t  u16DstRegAddr;
    uint8_t   u8RegData;
}I2cSendData;

#endif
