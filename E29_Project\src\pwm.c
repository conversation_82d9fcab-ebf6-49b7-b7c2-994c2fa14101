/**************************************************************************************************/
/**
 * @file     pwm.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#include "pwm.h"

#define PWM_TIM_ID     TIM1_ID

/*PWM Counter define----8K pwm output*/
#define PWM_MAX_COUNTER   5000
 
static void Tim_IndependentPWMOutputInit()
{      
    /* TIM PWM output config array*/
    TIM_PwmChannelConfig_t cPwmChConfig[1] = 
    {
        {
					.channelId = TIM_CHANNEL_7,
					.pwmModeConfig = TIM_PWM_LOW_TRUE_PULSE,
					.polarity = TIM_POL_HIGH,
					.compareValue = (PWM_MAX_COUNTER * 50 / 100),
					.faultCtrlChannelEnable = DISABLE,
					.ccvUpdateEnable = ENABLE
        }
    };
    
    /* TIM PWM output Config*/
    TIM_PwmConfig_t  cPwmConfig =
    {
        1,                  /*used channels number*/
        0,                  /*counter init value*/
        PWM_MAX_COUNTER,    /*counter max value*/
        cPwmChConfig        /*channel config pointer*/
    };
       
    PORT_PinmuxConfig(PORT_A, GPIO_13, PTA13_TIM1_CH7);
    
    /* set up-counting mode */
    TIM_CountingModeConfig(PWM_TIM_ID, TIM_COUNTING_UP);
    
    /* output complementary init*/
    TIM_OutputEdgeAlignedPwmConfig(PWM_TIM_ID, &cPwmConfig);
         
    /* pair0 output enable */
    TIM_ChannelOutputEnable(PWM_TIM_ID, TIM_CHANNEL_7);
}

static void Tim_SyncReloadFullCycle()
{
    /*TIM reload config*/
    TIM_ReloadConfig_t reloadConfig =
    {
        .reloadMode = TIM_RELOAD_FULL_CYCLE,
        .loadFrequency = 0,
        .numChannels = 0,
        .channelMatchConfig = NULL
    };
		
    /*TIM sync config for update ccv*/
    TIM_PwmSyncConfig_t  syncConfig = 
    {
        .cntinitSync = TIM_UPDATE_PWM_SYN,/* countinit sync */
        .outswcSync = TIM_UPDATE_PWM_SYN, /* outswc sync */
        .syncSWTriggerEnable = ENABLE,    /* software trigger sync  */
        .syncReloadEnable = DISABLE,      /* reload sync */
        .reloadConfig = &reloadConfig
    }; 

    /* sync config*/
    TIM_SyncConfig(PWM_TIM_ID, &syncConfig); 
}

static void Tim_SyncReloadHalfCycle()
{
    TIM_ChannelMatchConfig_t  channelMatchConfig =
    {
        TIM_CHANNEL_7, ENABLE
    };
		
    /*TIM reload config*/
    TIM_ReloadConfig_t   reloadConfig =
    {
        TIM_RELOAD_HALF_CYCLE,
        2,
        1,
        &channelMatchConfig
    };
    
    /*TIM sync config for update ccv*/
    TIM_PwmSyncConfig_t  syncConfig = 
    {
        TIM_UPDATE_PWM_SYN,		/* cntinit sync */
        TIM_UPDATE_PWM_SYN, 	/* outswc sync */
        DISABLE,            	/* software trigger sync  */
        ENABLE,           		/* reload sync */
        &reloadConfig
    };
		
    /* reload config*/
    TIM_SyncConfig(PWM_TIM_ID, &syncConfig);
}

void Pwm_Init(void)
{
		/*TIM clock source select*/
    
    CLK_ModuleSrc(CLK_TIM1, CLK_SRC_OSC40M);
    CLK_SetClkDivider(CLK_TIM1, CLK_DIV_1);
    /*TIM module enable*/
    SYSCTRL_ResetModule(SYSCTRL_TIM1);
    SYSCTRL_EnableModule(SYSCTRL_TIM1);
	
		Tim_IndependentPWMOutputInit();
	
		/*start TIM PWM*/
    TIM_StartCounter(PWM_TIM_ID, TIM_CLK_SOURCE_SYSTEM, TIM_CLK_DIVIDE_1);
	
		/* hcv value */
    TIM_SetHCVal(PWM_TIM_ID, 0x500);
	
    /* reload update duty cycle */
    Tim_SyncReloadHalfCycle();
}

void Ex_PWMChangeDutyCycle(uint8_t BacklightLevel)
{
		/* loaden = 1 */
    TIM_ReloadSyncCmd(PWM_TIM_ID, ENABLE);
    
    /* reset duty cycle */
    TIM_SetCCVal(PWM_TIM_ID, TIM_CHANNEL_7, ((PWM_MAX_COUNTER * BacklightLevel / 100)));
}