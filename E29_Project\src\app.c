#include "app.h"
#include "i2c.h"
#include "wdog.h"
#include "can.h"
#include "math.h"
#include "uart.h"
#include "SysTick.h"

//I2C
uint8_t u8ModeCheckI2CFlag = 0u;
uint8_t u8BKLCheckI2CFlag = 0u;
uint8_t u8FaultCheckI2CFlag = 0u;
uint8_t u8HWCheckI2CFlag = 0u;
uint8_t u8SWCheckI2CFlag = 0u;
uint8_t u8PWCheckI2CFlag = 0u;
uint8_t u8TempCheckI2CFlag = 0u;
uint8_t u8ProCheckI2CFlag = 0u;
uint8_t u8UpdateI2CFlag = 0u;
uint8_t u8BLKREGValFlag = 0u;
uint8_t u8DesFaultFlag = 0u;

//Task
uint8_t u8CANWriteTask = 0;
uint8_t u8CANReadTask = 0;
uint8_t u8SwitchColorTask = 0;
uint8_t u8WriteBKLTask = 0;
uint8_t u8CanDesTask = 0u;
uint8_t u8CanBLKTask = 0u;
uint8_t u8CANWriteHWTask = 0u;      //硬件号
uint8_t u8CANWriteSNTask = 0u;      //序列号
uint8_t u8CANClearTask = 0u;

//SwitchColor
bool VPG_ColorRedFlag = false;
bool VPG_ColorGreenFlag = false;
bool VPG_ColorBlueFlag = false;
bool VPG_ColorBlackFlag = false;
bool VPG_ColorWhiteFlag = false;

//i2c Timer
static uint16_t u16I2CTimeOut = 0u;

//State
uint8_t u8UartUpdateState = 0;
static uint8_t u8SendCanState = 0;

//EOL
uint8_t au8EOLTestStart[] = {0xAA, 0x00, 0x55};
uint8_t au8EOLTestCAN[8] = {0xAA, 0x01, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00};
uint8_t au8EOLTestEnd[] = {0xAA, 0x01, 0x55};
uint8_t u8ButtonNext[3] = {0xAA,0x02,0x55};      //下一张
uint8_t u8ButtonPrevious[3] = {0xAA,0x03,0x55};  //上一张

void I2c0_vMain(void)
{
    static uint8_t u8Step = 0u;
    static  I2CWorkState eCurrentWS = Mode_Idle;
    static uint8_t au8BKLData[8u] = {0x03, 0x0Au, 0u, 0u, 0u, 0u, 0u, 0u};
    static uint8_t u8BLKData = 0x0Au;
    static uint16_t u16DesRegTaskadd = 0u;

    if(u8CANWriteTask && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CANWriteTask = 0u;
        eCurrentWS = Mode_CanWriteTask;   
        u8Step = 3u;
    }
    else if(u8CANReadTask && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CANReadTask = 0u;
        eCurrentWS = Mode_CanReadTask;   
        u8Step = 3u;
    }
    else if(u8SwitchColorTask && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8SwitchColorTask = 0u;
        eCurrentWS = Mode_SwitchColor;   
        u8Step = 3u;
    }
    else if(u8WriteBKLTask && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8WriteBKLTask = 0u;
        eCurrentWS = Mode_ButtonBKL;   
        u8Step = 3u;
    }
    else if((u8CanDesTask == 1) && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CanDesTask = 0;
        eCurrentWS = Mode_UartDes;
        u8Step = 3u;
    }
    else if((u8CanBLKTask == 1) && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CanBLKTask = 0;
        eCurrentWS = Mode_CanBLKFault;
        u8Step = 3u;
    }
    else if((u8CANWriteHWTask == 1) && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CANWriteHWTask = 0;
        eCurrentWS = Mode_CanWriteHW;
        u8Step = 3u;
    }
    else if((u8CANWriteSNTask == 1) && boI2c0IsIdle && (eCurrentWS == Mode_Idle))
    {
        u8CANWriteSNTask = 0;
        eCurrentWS = Mode_CanWriteSN;
        u8Step = 3u;
    }
    else
    {

    }

    if (eCurrentWS == Mode_CanWriteTask)
    {
        if (u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if (u8Step == 2u)
        {
            if(au8CanData[0] == 0x01)
            {
                au8BKLData[0] = au8CanData[0];
                au8BKLData[1] = au8CanData[1];
                au8BKLData[2] = au8CanData[2];
                au8BKLData[3] = 0x00;
                au8BKLData[4] = 0x00;
                au8BKLData[5] = 0x00;
                au8BKLData[6] = 0x00;
                au8BKLData[7] = au8BKLData[0u]^au8BKLData[1u]^au8BKLData[2u]^au8BKLData[3u]^au8BKLData[4u]^au8BKLData[5u]^au8BKLData[6u];
            }
            else if(au8CanData[0] == 0x03)
            {
                au8BKLData[0] = au8CanData[0];
                au8BKLData[1] = au8CanData[2];
                au8BKLData[2] = 0x00;
                au8BKLData[3] = 0x00;
                au8BKLData[4] = 0x00;
                au8BKLData[5] = 0x00;
                au8BKLData[6] = 0x00;
                au8BKLData[7] = au8BKLData[0u]^au8BKLData[1u]^au8BKLData[2u]^au8BKLData[3u]^au8BKLData[4u]^au8BKLData[5u]^au8BKLData[6u];
            }
            else if(au8CanData[0] == 0xF5)
            {
                au8BKLData[0] = au8CanData[0];
                au8BKLData[1] = 0x00;
                au8BKLData[2] = 0x00;
                au8BKLData[3] = 0x00;
                au8BKLData[4] = 0x00;
                au8BKLData[5] = 0x00;
                au8BKLData[6] = 0x00;
                au8BKLData[7] = au8BKLData[0u]^au8BKLData[1u]^au8BKLData[2u]^au8BKLData[3u]^au8BKLData[4u]^au8BKLData[5u]^au8BKLData[6u];
            }
            else if(au8CanData[0] == 0xF6)
            {
                au8BKLData[0] = au8CanData[0];
                au8BKLData[1] = 0x00;
                au8BKLData[2] = 0x00;
                au8BKLData[3] = 0x00;
                au8BKLData[4] = 0x00;
                au8BKLData[5] = 0x00;
                au8BKLData[6] = 0x00;
                au8BKLData[7] = au8BKLData[0u]^au8BKLData[1u]^au8BKLData[2u]^au8BKLData[3u]^au8BKLData[4u]^au8BKLData[5u]^au8BKLData[6u];
            }
            else
            {

            }
            Ex_MstWriteArray(I2C1_ID, (uint8_t*)&au8BKLData[0u]);
            u8Step = 1u;
        }
        else if (u8Step == 1u)
        {
            eCurrentWS = Mode_Idle;
            u8Step = 0u;
        }
        else
        {
            /* Do nothing */
        }
    }
    else if(eCurrentWS == Mode_CanReadTask)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            switch (u8CanReadData)
            {
            case 0x00:
                u8ModeCheckI2CFlag = 1u;
                break;
            case 0x02u:
                u8BKLCheckI2CFlag = 1u;
                break;
            case 0x04u:
                u8FaultCheckI2CFlag = 1u;
                break;
            case 0x07u:
                u8HWCheckI2CFlag = 1u;
                break;
            case 0x08u:
                u8SWCheckI2CFlag = 1u;
                break;
            case 0x09u:
                u8PWCheckI2CFlag = 1u;
                break;
            case 0x0Au:
                u8ProCheckI2CFlag = 1u;
                break;
            case 0xF0u:
                u8TempCheckI2CFlag = 1u;
                break;
            default:
                break;
            }
            Ex_MstReadArray(I2C1_ID, (uint8_t*)&u8CanReadData);
            u8Step = 1u;
        }
        else if(u8Step == 1u && !u8ModeCheckI2CFlag && !u8BKLCheckI2CFlag && !u8FaultCheckI2CFlag && !u8HWCheckI2CFlag
        && !u8SWCheckI2CFlag && !u8PWCheckI2CFlag && !u8ProCheckI2CFlag && !u8TempCheckI2CFlag)
        { 
            u8SendCanState = 1;
            eCurrentWS = Mode_Idle;
            u8Step = 0;
        }
    }
    else if(eCurrentWS == Mode_SwitchColor)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C0_ID);
            I2C_SetTargetAddr(I2C0_ID, DEV_SER);
            I2C_Enable(I2C0_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            u8Step = 1u;
            if(au8CanData[0u] == 0x04)
            {
                Uart_Transmit(u8ButtonPrevious , 3);
            }
            else if(au8CanData[0u] == 0x05)
            {
                Uart_Transmit(u8ButtonNext , 3);
            }
            else
            {

            }
        }
        else if(u8Step == 1u)
        {
            I2C_Disable(I2C0_ID);
            I2C_SetTargetAddr(I2C0_ID, DEV_DES);
            I2C_Enable(I2C0_ID);
            eCurrentWS = Mode_Idle;
            u8Step = 0u;
        }
        else
        {
            /* Do nothing */
        }
    }
    else if(eCurrentWS == Mode_ButtonBKL)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            u8Step = 1u;
            if(u8BLKData >= 10 && u8BLKData <= 0x64)
            {
                u8BLKData = u8BLKData - 10;
            }
            else  if(u8BLKData == 0) 
            {
                u8BLKData = 1;
            }
            else  if(u8BLKData == 1)
            {
                u8BLKData = 100;
            }
            else
            {
                
            }

            au8BKLData[0u] = 0x03u;
            au8BKLData[1u] = u8BLKData;
            au8BKLData[2u] = 0x00u;
            au8BKLData[3u] = 0x00u;
            au8BKLData[4u] = 0x00u;
            au8BKLData[5u] = 0x00u;
            au8BKLData[6u] = 0x00u;
            au8BKLData[7] = au8BKLData[0u]^au8BKLData[1u]^au8BKLData[2u]^au8BKLData[3u]^au8BKLData[4u]^au8BKLData[5u]^au8BKLData[6u];
            Ex_MstWriteArray(I2C1_ID, (uint8_t*)&au8BKLData[0u]);
        }
        else if (u8Step == 1u)
        {
            u8Step = 0;
            eCurrentWS = Mode_Idle;
        }
    }
    else if(eCurrentWS == Mode_UartDes)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C0_ID);
            I2C_SetTargetAddr(I2C0_ID, DEV_DES);
            I2C_Enable(I2C0_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            u16DesRegTaskadd = 0x13;
            u8DesFaultFlag = 1;
            Ex_MstReadBuffer(I2C0_ID, (uint8_t*)&u16DesRegTaskadd);
            u8Step = 1u;
        }
        else if(u8Step == 1u && !u8DesFaultFlag)
        {
            I2C_Disable(I2C0_ID);
            I2C_SetTargetAddr(I2C0_ID, DEV_DES);
            I2C_Enable(I2C0_ID);
            u8Step = 0u;
        }
        else if(u8Step == 0u)
        {
            eCurrentWS = Mode_Idle;
            u8SendCanState = 1;
        }
    }
    else if(eCurrentWS == Mode_CanBLKFault)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            u8BLKREGValFlag = 1;
            Ex_MstReadArray(I2C1_ID, (uint8_t*)&u8CanReadData);
            u8Step = 1u;
        }
        else if(u8Step == 1u && !u8BLKREGValFlag)
        {
            u8Step = 0u;
        }
        else if(u8Step == 0u)
        {
            eCurrentWS = Mode_Idle;
            u8SendCanState = 1;
        }
    }
    else if(eCurrentWS == Mode_CanWriteHW)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            au8CanDataHW[15] = CalculateChecksum(0,14,au8CanDataHW);
            Ex_MstWriteHWArrays(I2C1_ID, (uint8_t*)&au8CanDataHW[0u]);
            u8Step = 1u;
        }
        else if(u8Step == 1u )
        {   
            eCurrentWS = Mode_Idle;
            u8Step = 0u;
        }
        else 
        {

        }
    }
    else if(eCurrentWS == Mode_CanWriteSN)
    {
        if(u8Step == 3u)
        {
            I2C_Disable(I2C1_ID);
            I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
            I2C_Enable(I2C1_ID);
            u8Step = 2u;
        }
        else if(u8Step == 2u)
        {
            au8CanDataSN[31] = CalculateChecksum(0,30,au8CanDataSN);
            Ex_MstWriteSNArrays(I2C1_ID, (uint8_t*)&au8CanDataSN[0u]);
            u8Step = 1u;
        }
        else if(u8Step == 1u )
        {
            eCurrentWS = Mode_Idle;
            u8Step = 0u;
        }
        else 
        {
        }
    }
    else
    {

    }
}

void App_vWatchDog(void)
{
    static uint8_t u8Count = 0u;
    u8Count += 1u;
    /* 1000ms periodic */
    if(u8Count >= 100u)
    {
        u8Count = 0u;
        WDOG_Refresh();
    }
}

void App_vClearI2CRecvFlag(void) // 10ms task
{
    if (u8ModeCheckI2CFlag ||u8BKLCheckI2CFlag ||u8FaultCheckI2CFlag ||u8HWCheckI2CFlag ||u8SWCheckI2CFlag ||
    u8PWCheckI2CFlag ||u8ProCheckI2CFlag ||u8TempCheckI2CFlag ||u8BLKREGValFlag || u8DesFaultFlag || u8UpdateI2CFlag)
    {
        if (u16I2CTimeOut < 100) // 1s
        {
            u16I2CTimeOut++;
        }
        else
        {
            u8ModeCheckI2CFlag = 0;
            u8BKLCheckI2CFlag = 0;
            u8FaultCheckI2CFlag = 0;
            u8HWCheckI2CFlag = 0;
            u8SWCheckI2CFlag = 0;
            u8PWCheckI2CFlag = 0;
            u8ProCheckI2CFlag = 0;
            u8TempCheckI2CFlag = 0;
            u8BLKREGValFlag = 0;
            u8DesFaultFlag = 0;
            u8UpdateI2CFlag = 0;
            u16I2CTimeOut = 0;
        }
    }
    else
    {
        u16I2CTimeOut = 0;
    }
}

void App_vSendCan(void)
{
    char chArrayTestSW[16] = {'E','2','9','_','E','o','l','_','v','1','.','0','.','0'};    
    uint8_t u8ArrayTestSW[16] = {0}; 
    uint8_t u8CANMessage[8] = {0};
    static uint8_t u8SetpCanSend = 0u;

    if (u8InitFlag == 5 && u8SendCanState == 1)
    {
        switch (u8CanReadData)
        {
        case 0x00u:
            u8CANMessage[0] = u8ModeRecvBuf[0];
            u8CANMessage[1] = u8ModeRecvBuf[1];
            u8CANMessage[2] = 0x00;
            u8CANMessage[3] = 0x00;
            u8CANMessage[4] = 0x00;
            u8CANMessage[5] = 0x00;
            u8CANMessage[6] = 0x00;
            u8CANMessage[7] = 0x00;
            CAN_Send_Msg(0x500, &u8CANMessage[0]);
            u8SendCanState = 0;
            break;
        case 0x02u:
            u8CANMessage[0] = u8BKLRecvBuf[0];
            u8CANMessage[1] = 0x00;
            u8CANMessage[2] = u8BKLRecvBuf[1];
            u8CANMessage[3] = 0x00;
            u8CANMessage[4] = 0x00;
            u8CANMessage[5] = 0x00;
            u8CANMessage[6] = 0x00;
            u8CANMessage[7] = 0x00;
            CAN_Send_Msg(0x500, &u8CANMessage[0]);
            u8SendCanState = 0;
            break;
        case 0x04u:
            if (u8SetpCanSend == 0)
            {
                u8SetpCanSend = 1;
                CAN_Send_Msg(0x501, &u8FaultRecvBuf[0]);
            }
            else if (u8SetpCanSend == 1)
            {
                u8SetpCanSend = 0;
                u8FaultRecvBuf[13] = 0x00;
                CAN_Send_Msg(0x502, &u8FaultRecvBuf[8]);
                u8SendCanState = 0;
            }
            break;
        case 0x07u:
            if (u8SetpCanSend == 0)
            {
                u8SetpCanSend = 1;
                CAN_Send_Msg(0x503, &u8HWRecvBuf[0]);
            }
            else if (u8SetpCanSend == 1)
            {
                u8SetpCanSend = 0;
                u8HWRecvBuf[15] = 0x00;
                CAN_Send_Msg(0x504, &u8HWRecvBuf[8]);
                u8SendCanState = 0;
            }
            break;
        case 0x08u:
            if (u8SetpCanSend == 0)
            {
                u8SetpCanSend = 1;
                CAN_Send_Msg(0x505, &u8SWRecvBuf[0]);
            }
            else if (u8SetpCanSend == 1)
            {
                u8SetpCanSend = 0;
                u8SWRecvBuf[15] = 0x00;
                CAN_Send_Msg(0x506, &u8SWRecvBuf[8]);
                u8SendCanState = 0;
            }
            break;
        case 0x09u:
            if (u8SetpCanSend == 0)
            {
                u8SetpCanSend = 1u;
                CAN_Send_Msg(0x509, &u8PartNumRecvBuf[0]);
            }
            else if (u8SetpCanSend == 1)
            {
                u8SetpCanSend = 2u;
                CAN_Send_Msg(0x50A, &u8PartNumRecvBuf[8]);
            }
            else if (u8SetpCanSend == 2)
            {
                u8SetpCanSend = 3u;
                CAN_Send_Msg(0x50B, &u8PartNumRecvBuf[16]);
            }
            else if (u8SetpCanSend == 3)
            {
                u8SetpCanSend = 0;
                u8PartNumRecvBuf[31] = 0x00;
                CAN_Send_Msg(0x50C, &u8PartNumRecvBuf[24]);
                u8SendCanState = 0;
            }
            break;
        case 0x0Au:
            if (u8SetpCanSend == 0)
            {
                u8SetpCanSend = 1u;
                CAN_Send_Msg(0x507, &u8ProRecvBuf[0]);
            }
            else if (u8SetpCanSend == 1)
            {
                u8SetpCanSend = 0u;
                u8ProRecvBuf[15] = 0x00;
                CAN_Send_Msg(0x508, &u8ProRecvBuf[8]);
                u8SendCanState = 0;
            }
            break;
        case 0xF0u:
            u8CANMessage[0] = u8TempRecvBuf[0];
            u8CANMessage[1] = 0x00;
            u8CANMessage[2] = 0x00;
            u8CANMessage[3] = u8TempRecvBuf[1];
            u8CANMessage[4] = u8TempRecvBuf[2];
            u8CANMessage[5] = 0x00;
            u8CANMessage[6] = 0x00;
            u8CANMessage[7] = 0x00;
            CAN_Send_Msg(0x500, &u8CANMessage[0]);
            u8SendCanState = 0;
            break;
        case 0xF1:
            u8CANMessage[0] = 0xF1;
            u8CANMessage[1] = 0x00;
            u8CANMessage[2] = 0x00;
            u8CANMessage[3] = 0x00;
            u8CANMessage[4] = 0x00;
            u8CANMessage[5] = u8DesFaultBuf[0];
            u8CANMessage[6] = 0x00;
            u8CANMessage[7] = 0x00;
            CAN_Send_Msg(0x500, &u8CANMessage[0]);
            u8SendCanState = 0;
            break;
        case 0xF2:
            u8CANMessage[0] = u8BLKREGBuf[0];
            u8CANMessage[1] = 0x00;
            u8CANMessage[2] = 0x00;
            u8CANMessage[3] = 0x00;
            u8CANMessage[4] = 0x00;
            u8CANMessage[5] = 0x00;
            u8CANMessage[6] = u8BLKREGBuf[1];
            u8CANMessage[7] = 0x00;
            CAN_Send_Msg(0x500, &u8CANMessage[0]);
            u8SendCanState = 0;
            break;
        default:
            u8SendCanState = 0;
            break;
        }
    }
    if (u8InitFlag == 5 && u8CanReadData == 0xF3 && u8CanVersion == 1)
    {
        if (u8SetpCanSend == 0)
        {
            u8SetpCanSend = 1u;
            memcpy(&u8ArrayTestSW[0], &chArrayTestSW[0u], 14u);
            CAN_Send_Msg(0x600, &u8ArrayTestSW[0]);
        }
        else if (u8SetpCanSend == 1)
        {
            u8SetpCanSend = 0u;
            u8CanVersion = 0; 
			memcpy(&u8ArrayTestSW[0], &chArrayTestSW[0u], 14u);
            CAN_Send_Msg(0x601, &u8ArrayTestSW[8]);
        }
    }
}

#if 1
void App_vUart(void)    //EOL跟树莓派的串口协议
{
    static uint8_t u8UartCount = 100;
    static bool boUartState = false;
    static uint8_t u8UartCANCount = 0;
    static uint16_t u16EOLTestCount = 0;
    static bool boEOLTestState = false;
    if (u8StartEOLTest && boEOLTestState == false)
    {
        boEOLTestState = true;
        Uart_Transmit(au8EOLTestStart, 3);
    }
    else if (u8StartEOLTest && boEOLTestState == true)
    {
        u16EOLTestCount += 1;
        if (u16EOLTestCount <= 20)
        {
            if (u8UartRecvState)
            {
                if (u8UartDataArray[0] == 0xAA && u8UartDataArray[1] == 0x00 && u8UartDataArray[2] == 0x55)
                {
                    u8StartEOLTest = 0;
                    u16EOLTestCount = 0;
                }
            }
        }
        else if (u16EOLTestCount > 20)
        {
            u16EOLTestCount = 0;
            Uart_Transmit(au8EOLTestStart, 3);
        }
    }
    else if (u8UartRecvState)
    {
        u8UartRecvState = 0;
        if (u8UartDataArray[0] == 0xAA && u8UartDataArray[1] == 0x01 && u8UartDataArray[2] == 0x55)
        {
            Uart_Transmit(au8EOLTestEnd, 3);
            boUartState = true;
        }
    }

    if (boUartState)
    {
        u8UartCount--;
        if (u8UartCount == 9)
        {
            if (u8UartCANCount < 3)
            {
                CAN_Send_Msg(0x721, au8EOLTestCAN);
            }
            else
            {
                boUartState = false;
                u8UartRecvState = 0;
                u8UartCANCount = 0;
                u8UartCount = 10;
            }
        }
        else if (u8UartCount == 0)
        {
            u8UartCount = 10;
            u8UartCANCount++;
        }
        else
        {
        }
    }
}
#endif

// 去抖动函数，返回按钮的稳定状态
bool DebounceButton(PORT_ID_t port, PORT_GPIONO_t gpioNo)
{
    static bool lastState = GPIO_HIGH; // 上一次读取的状态
    uint8_t debounceDelay = 3; // 去抖动延时，根据按钮特性调整
    uint8_t readCount = 0;

    if (GPIO_ReadPinLevel(port, gpioNo) == GPIO_LOW)
    {
        while (readCount < debounceDelay)
        {
            if(GPIO_ReadPinLevel(port, gpioNo) == GPIO_LOW)
            {
                readCount++;
                lastState  = GPIO_LOW;
            }
            else
            {
                lastState = GPIO_HIGH;
                break;
            }
        }
    }
    else
    {
        lastState = GPIO_HIGH;
    }

    // 返回最终稳定的状态
    return lastState;
}

void App_vButton(void)
{
    static uint8_t u8ButtonSW1 = GPIO_HIGH;
    static uint8_t u8ButtonSW3 = GPIO_HIGH;
    static uint8_t u8ButtonSW1old = GPIO_HIGH;
    static uint8_t u8ButtonSW3old = GPIO_HIGH;
    static uint16_t u16Buttontime = 0u;
    static uint8_t u8ButtonSW1ON = 0u;
    static uint8_t u8ButtonSW3ON = 0u;

    if (u8InitFlag == 5)
    {

        u8ButtonSW1 = GPIO_ReadPinLevel(PORT_D, GPIO_2);
        u8ButtonSW3 = GPIO_ReadPinLevel(PORT_C, GPIO_3);

        if (u8ButtonSW1old == GPIO_HIGH && u8ButtonSW1 == GPIO_LOW)
        {
            Uart_Transmit(u8ButtonPrevious , 3);
        }

        if (u8ButtonSW3old == GPIO_HIGH && u8ButtonSW3 == GPIO_LOW)
        {
            Uart_Transmit(u8ButtonNext , 3);
        }

        // if (u8ButtonSW1old == GPIO_HIGH && u8ButtonSW1 == GPIO_LOW && u8ButtonSW1ON == 0 && u16Buttontime == 0)
        // {
        //     u8ButtonSW1ON = 1;
        //     Uart_Transmit(u8ButtonPrevious , 3);
        //     // Uart_Transmit(u8ButtonPrevious , 3);
        //     // Uart_Transmit(u8ButtonNext , 3);
        //     // Uart_Transmit(u8ButtonNext , 3);
        // }
        
        // if (u8ButtonSW3old == GPIO_HIGH && u8ButtonSW3 == GPIO_LOW && u8ButtonSW3ON == 0 && u16Buttontime == 0)
        // {
        //     u8ButtonSW3ON = 1;
        //     Uart_Transmit(u8ButtonNext , 3);
        //     // Uart_Transmit(u8ButtonNext , 3);
        // }

        if(u8ButtonSW1ON == 1 || u8ButtonSW3ON == 1)
        {
            u16Buttontime++;
        }

        if(u16Buttontime >200)
        {
            u16Buttontime = 0;
            u8ButtonSW1ON = 0;
            u8ButtonSW3ON = 0;
        }

        u8ButtonSW1old = u8ButtonSW1;
        u8ButtonSW3old = u8ButtonSW3;
        
    }
    if (u8SW2) 
    {
        u8WriteBKLTask = 1;
        // u8CanBLKTask = 1;
        // u8CANReadTask = 1;
        u8SW2 = 0u;
    }
    // else if (u8SW1) 
    // {
    //     u8SW1 = 0u;
    //     Uart_Transmit(u8ButtonPrevious , 3);
    // }
    // else if(u8SW3)
    // {
    //     u8SW3 = 0u;
    //     Uart_Transmit(u8ButtonNext , 3);
    // }
}

uint8_t u8FrameidLength = 0;                            
uint8_t u8MstRecvAckBuf[20] = {0};                      //主机回读ACK
uint8_t u8Check_Flag = 0;                               //校验标志位

bool boTimer_200ms = false;
bool boTimer_2ms = false;
bool boTimer_5ms = false;
bool boTimer_100ms = false;
uint32_t value_1 = 0;
uint32_t value_2 = 0;
uint32_t value_3 = 0;
uint32_t value_4 = 0;
void App_vUpdateFraneSend(void)
{
    static bool boFrist_Frame = false;             // 首帧
    static bool boData_Frame = false;              // 数据帧
    static bool boCheck_Result = Failed;           // 校验结果
    static uint8_t u8Try_Number = 0;               // 连续出错次数
    static uint8_t u8Download_Data[64] = {0};      // 数据包
    static uint8_t u8Download_SendFrameID = 0x05u; // MCU固件升级
    static uint8_t u8Download_ReadFrameID = 0x06u; // MCU固件升级响应
    static uint8_t u8Download_Seq = 0x01u;                // 0x01 起始帧
    static uint8_t *pUpdate = NULL;
    static uint16_t u16Index = 2;
    static uint16_t u16Num_iterations = 0;
    static uint32_t u32Total_bytes = 0;
    static uint8_t u8Remaining_bytes = 0;

    // Update init
    pUpdate = (uint8_t *)(uint32_t)UPDATE_DATA_START_ADDR;      //0x00010000
    u32Total_bytes = UPDATE_DATA_SIZE;       // 总字节数
    u16Num_iterations = u32Total_bytes / 61; // 完整包数量
    u8Remaining_bytes = u32Total_bytes % 61; // 最后一个包的字节数

    if (boFrist_Frame == false)
    {
        if (boCheck_Result == Failed && u8Try_Number <= 10)
        {
            // 主机数据包64个字节
            u8Download_Data[0] = u8Download_SendFrameID;
            u8Download_Data[1] = u8Download_Seq;
            memcpy(u8Download_Data + 2, pUpdate, 61);
            u8Download_Data[63] = CalculateChecksum(0, 62, u8Download_Data);

            // 打印写的字节
            Uart_Transmit(u8Download_Data, 64);

            // 开始写
            Ex_MstWriteArrays(I2C1_ID, (uint8_t *)&u8Download_Data, 64); // 发送数据

            boTimer_200ms = true;   // 开始计时
            boCheck_Result = OK;
        }

        if (boTimer_200MsFlag) // 尾帧发送后已经过了200ms
        {
            u8FrameidLength = 8;
            u8UpdateI2CFlag = 1;
            Ex_MstReadArray(I2C1_ID, (uint8_t *)&u8Download_ReadFrameID); // 发送FrameID 0X06 回读下载状态
            boTimer_200MsFlag = false;
            boTimer_5ms = true;     // 开始计时
        }

        if (boTimer_5MsFlag) // 发送主机读后5ms检查是否接收到读中断数据
        {
            boTimer_5MsFlag = false;
            if (u8Check_Flag == 1) // 读到从机的数据
            {
                u8Check_Flag = 0;                                               // 等待接收置位，置位后进入检验流程
                if (u8MstRecvAckBuf[1] == 0x01u && u8MstRecvAckBuf[2] == 0x00u) // 收到的8个字节数组，对Seq和ACK进行检查
                {
                    // check_result = OK;
                    u8Try_Number = 0;
                    boFrist_Frame = true;
                    boCheck_Result = Failed;
                    u8Download_Seq = 0x02;
                }
                else // Seq和ACK进行检查，seq和ack不对重新发送
                {
                    boCheck_Result = Failed;
                    u8Try_Number += 1;
                }
                memset(u8MstRecvAckBuf, 0, u8FrameidLength); // 清回读Buffer
            }
            else // 未读到从机的数据，重新发送首帧
            {
                boCheck_Result = Failed;
                u8Try_Number += 1;
            }
        }
    }

    // 继续发送数据帧
    if (boFrist_Frame == true && boData_Frame == false)
    {
        if ((u16Index - 1) < u16Num_iterations)
        {
            if (boCheck_Result == Failed && u8Try_Number < 10)
            {
                u8Download_Data[0] = u8Download_SendFrameID;
                u8Download_Data[1] = u8Download_Seq;
                memcpy(u8Download_Data + 2, (pUpdate + (u16Index - 1) * 61), 61); // 复制61个字节到Download_Data中（前两个字节保留
                u8Download_Data[63] = CalculateChecksum(0, 62, u8Download_Data);  // 计算校验和并赋值给最后一个字节

                Uart_Transmit(u8Download_Data, 64);

                Ex_MstWriteArrays(I2C1_ID, (uint8_t *)&u8Download_Data, 64); // 发送数据
                SysTick_GetCurCount(&value_1);

                boTimer_2ms = true;
                boCheck_Result = OK; // 开始计时
            }

            if (boTimer_2MsFlag) // 发送后已经过了2ms
            {
                SysTick_GetCurCount(&value_2);
                if (value_1 > value_2)
                {
                    if (value_1 - value_2 >= 128000)
                    {
                        SysTick_GetCurCount(&value_3);
                        u8FrameidLength = 8;
                        u8UpdateI2CFlag = 1;
                        Ex_MstReadArray(I2C1_ID, (uint8_t *)&u8Download_ReadFrameID); // 发送FrameID 0X06 回读下载状态
                        boTimer_2MsFlag = false;
                        boTimer_5ms = true;
                    }
                }
                else // value_1 < value_2
                {
                    if ((value_1 + 0x00FFFFFFu - value_2) >= 128000)
                    {
                        SysTick_GetCurCount(&value_4);
                        u8FrameidLength = 8;
                        u8UpdateI2CFlag = 1;
                        Ex_MstReadArray(I2C1_ID, (uint8_t *)&u8Download_ReadFrameID); // 发送FrameID 0X06 回读下载状态
                        boTimer_2MsFlag = false;
                        boTimer_5ms = true;
                    }
                }
            }

            if (boTimer_5MsFlag) // 发送主机读后5ms检查是否接收到读中断数据
            {
                boCheck_Result = Failed;
                boTimer_5MsFlag = false;
                if (u8Check_Flag == 1)
                {
                    u8Check_Flag = 0;
                    if (u8MstRecvAckBuf[1] == u8Download_Seq && u8MstRecvAckBuf[2] == 0x00u) // 收到的8个字节数组，对Seq和ACK进行检查
                    {
                        u8Try_Number = 0;

                        if (u8Download_Seq == 255)
                        {
                            u8Download_Seq = 1; // seq只支持2-255
                        }

                        u8Download_Seq++;
                        u16Index++;
                    }
                    else
                    {
                        u8Try_Number += 1;
                    }
                    memset(u8MstRecvAckBuf, 0, u8FrameidLength); // 清回读Buffer
                }
                else // 未读到从机的数据，重新发送本帧
                {
                    u8Try_Number += 1;
                }
            }
        }
        else
        {
            boData_Frame = true;
        }
    }

    if (boData_Frame == true) // 发送最后一帧
    {
        if (u8Remaining_bytes > 0)
        {
            if (boCheck_Result == Failed && u8Try_Number < 10)
            {
                u8Download_Seq = 0x00;
                u8Download_Data[0] = u8Download_SendFrameID;
                u8Download_Data[1] = u8Download_Seq;
                memcpy(u8Download_Data + 2, (pUpdate + u16Num_iterations * 61), u8Remaining_bytes); // 复制剩余的字节到Download_Data中（前两个字节保留）
                memset(u8Download_Data + u8Remaining_bytes + 2, 0XFF, 61 - u8Remaining_bytes);      // 其余数据用0Xff填充
                u8Download_Data[63] = CalculateChecksum(0, 62, u8Download_Data);                    // 计算校验和
                Uart_Transmit(u8Download_Data, 64);
                Ex_MstWriteArrays(I2C1_ID, (uint8_t *)&u8Download_Data, 64);

                boTimer_200ms = true;   // 开始计时
                boCheck_Result = OK;
            }

            if (boTimer_200MsFlag) // 尾帧发送后已经过了200ms
            {
                u8FrameidLength = 8;
                u8UpdateI2CFlag = 1;
                Ex_MstReadArray(I2C1_ID, (uint8_t *)&u8Download_ReadFrameID); // 发送FrameID 0X06 回读下载状态
                boTimer_200MsFlag = false;
                boTimer_5ms = true;
            }

            if (boTimer_5MsFlag) // 发送主机读后5ms检查是否接收到读中断数据
            {
                boTimer_5MsFlag = false;
                if (u8Check_Flag == 1)
                {
                    u8Check_Flag = 0;
                    if ((u8MstRecvAckBuf[1] == u8Download_Seq && u8MstRecvAckBuf[2] == 0x00u) && (u8MstRecvAckBuf[3] == 0x00)) // 收到的8个字节数组，对Seq和ACK进行检查
                    {
                        u8UartUpdateState = Update_Complete;
                        boCheck_Result = OK;
                        u8Try_Number = 0;
                    }
                    else
                    {
                        boCheck_Result = Failed;
                        u8Try_Number += 1;
                    }
                    memset(u8MstRecvAckBuf, 0, u8FrameidLength); // 清回读Buffer
                }
                else // 未读到从机的数据，重新发送尾帧
                {
                    boCheck_Result = Failed;
                    u8Try_Number += 1;
                }
            }
        }
        else
        {
        }
    }

    if(u8Try_Number > 10)
    {
        u8UartUpdateState = Update_Complete;
        
    }

}

void App_vMain(void)                                    //10msTask
{
    I2c0_vMain();
    App_vWatchDog();
    App_vClearI2CRecvFlag();
    App_vUart();
    App_vSendCan();
    App_vButton();
}











