/**************************************************************************************************/
/**
 * @file     i2c.h
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/

#ifndef I2C_H
#define I2C_H

#include "Z20K11xM_drv.h"
#include "Z20K11xM_i2c.h"
#include "string.h"
#include "stdbool.h"
#include "deserializers.h"
#include "diag.h"

extern bool boI2c0IsIdle;
extern void I2c_Init(void);

/* master */
static void MstRecvByType(I2C_Id_t i2cNo, uint16_t len, I2C_RestartStop_t restartStopType);
static void MstSendByType(I2C_Id_t i2cNo, uint8_t * gTxBuffer, uint16_t len, I2C_RestartStop_t restartStopType);
extern void Ex_MstWriteBuffer(I2C_Id_t i2cNo, uint8_t * DestAddr, uint8_t * Data);
extern void Ex_MstWriteArray(I2C_Id_t i2cNo, uint8_t * Data);
extern void Ex_MstReadArray(I2C_Id_t i2cNo, uint8_t * DestAddr);
extern void Ex_MstReadBuffer(I2C_Id_t i2cNo, uint8_t * DestAddr);
extern void Ex_MstWriteArrays(I2C_Id_t i2cNo, uint8_t * Data,uint8_t length);
extern void Ex_MstWriteSNArrays(I2C_Id_t i2cNo, uint8_t * Data);
extern void Ex_MstWriteHWArrays(I2C_Id_t i2cNo, uint8_t * Data);

/* callback */
static void I2C_MasterRecvCallBack(void);
static void I2C_MasterStopGeneratedCallBack(void);

//i2c
extern uint8_t u8ModeCheckI2CFlag;
extern uint8_t u8BKLCheckI2CFlag;
extern uint8_t u8FaultCheckI2CFlag;
extern uint8_t u8HWCheckI2CFlag;;
extern uint8_t u8SWCheckI2CFlag;
extern uint8_t u8PWCheckI2CFlag;
extern uint8_t u8TempCheckI2CFlag;
extern uint8_t u8ProCheckI2CFlag;
extern uint8_t u8UpdateI2CFlag;
extern uint8_t u8BLKREGValFlag;
extern uint8_t u8DesFaultFlag;

#endif /* I2C_H */

