/**************************************************************************************************/
/**
 * @file     deserializers.h
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#ifndef DESERIALIZERS_H
#define DESERIALIZERS_H

#include "Z20K11xM_drv.h"
#include "i2c.h"

#define DES_REG_ADDR_BYTE	2

#if 0
typedef struct{
		uint8_t  OLDI_SPL_POL			:	1	;		//rw
		uint8_t  OLDI_SPL_MODE		:	2	;		//rw
		uint8_t  OLDI_SPL_EN			:	1	;		//rw
		uint8_t  OLDI_SWAP_AB			:	1	;		//rw
		uint8_t  OLDI_4TH_LANE		:	1	;		//rw
		uint8_t  OLDI_FORMAT			:	1	;		//rw
		uint8_t  OLDI_OUTSEL			:	1	;		//rw
}OLDI1_REG_DATA;

typedef struct{
		uint8_t  RX_RATE			:	2	;		//rw
		uint8_t  TX_RATE			:	2	;		//rw
		uint8_t  IIC_1_EN			:	1	;		//rw
		uint8_t  IIC_2_EN			:	1	;		//rw
		uint8_t  UNUSED				:	2	;		
}REG1_REG_DATA;

typedef struct{
		uint8_t  DATA				:	8	;	
}REG_REGCOMM_DATA;
#endif

typedef struct{
		uint8_t		CFG_BLOCK				:	1	;		//rw
		uint8_t		DEV_ADDR				:	7	;		//rw
}REG0_REG_DATA;

typedef struct{
		uint8_t  ADDR_MSB	;
		uint8_t  ADDR_LSB	;
}REG_ADDR;

struct REG0_REG{
		REG_ADDR Addr;
		REG0_REG_DATA Data;
};

#if 0
struct OLDI1_REG{
		REG_ADDR Addr;
		OLDI1_REG_DATA Data;
};

struct REG1_REG{
		REG_ADDR Addr;
		REG1_REG_DATA Data;
};

struct REG_REGCOMM{
		REG_ADDR Addr;
		REG_REGCOMM_DATA Data;
};
#endif

extern void Deserializers_Config(void);
static void MstWriteRegister(uint8_t * DestAddr, uint8_t WData);

#endif /* DESERIALIZERS_H */

