/**************************************************************************************************/
/**
 * @file     common_func.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#include "common_func.h"
 
/* delay function*/
void delay(volatile int cycles)
{
    /* Delay function - do nothing for a number of cycles */
    while(cycles--);
}

uint8_t CalculateChecksum(uint8_t StartByte, uint8_t EndByte, uint8_t* Data)
{
		int i = 0;
		uint8_t Checksum = 0;
	
		for(i = StartByte; i < (EndByte + 1); i ++)
		{
				Checksum = Checksum ^ Data[i];
		}
		return Checksum;
}

void FillZeroToArray(uint8_t StartByte, uint8_t EndByte, uint8_t* Data)
{
		int i = 0;

		for(i = StartByte; i < (EndByte + 1); i ++)
		{
				Data[i] = 0x00;
		}
}
