/**************************************************************************************************/
/**
 * @file      : security_access.h
 * @brief     : Security access protocol module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#ifndef SECURITY_ACCESS_H
#define SECURITY_ACCESS_H

#include <stdint.h>

/* Security access protocol state enumeration */
typedef enum {
    Security_IDLE = 0,        /* Idle state */
    Security_Step1_Sent = 1,  /* Step 1 sent: AA BB CC DD EE */
    Security_Step2_Sent = 2,  /* Step 2 sent: EE DD CC BB AA */
    Security_Complete = 3     /* complete */
} TestProtocolState_t;

/* Function declarations */
void Security_Access_Init(void);
void Enter_Security_Mode(void);
void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len);
void Calculate_Security_Key_AES128_CMAC(const uint8_t *seed_data, uint8_t *key_output);
void Prepare_Security_Key_Command(void);
TestProtocolState_t Get_Security_Access_State(void);

/* AES128-CMAC related functions */
void AES128_Encrypt_Block(const uint8_t *key, const uint8_t *input, uint8_t *output);
void Generate_Subkeys(const uint8_t *key, uint8_t *k1, uint8_t *k2);
void AES128_CMAC(const uint8_t *key, const uint8_t *message, uint16_t message_len, uint8_t *mac);

/* Test function for verification */
void Test_AES128_CMAC_Implementation(void);

#endif /* SECURITY_ACCESS1_H */
