/**************************************************************************************************/
/**
 * @file     deserializers.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/

#include "deserializers.h"

uint8_t ReadDataTest = 0;

// extern bool MstRecvFlag;


const struct REG0_REG REG0_config	= {
		{
			.ADDR_MSB = 0x00,
			.ADDR_LSB = 0x00
		},
		{
			.CFG_BLOCK = 0x0,
			.DEV_ADDR	= 0x4C
		}
};

#if 0
const struct OLDI1_REG OLDI1_config = {	
		{
			.ADDR_MSB = 0x01,
			.ADDR_LSB = 0xCE
		},
		{
			.OLDI_SPL_POL		= 0x0,
			.OLDI_SPL_MODE	= 0x0,//3
			.OLDI_SPL_EN		= 0x0,//1
			.OLDI_SWAP_AB		= 0x0,
			.OLDI_4TH_LANE	= 0x0,
			.OLDI_FORMAT		= 0x1,
			.OLDI_OUTSEL		= 0x0
		}
};

const struct REG1_REG REG1_config = {
		{
			.ADDR_MSB = 0x00,
			.ADDR_LSB = 0x01
		},
		{
		  .IIC_1_EN	= 0x1,		
		  .IIC_2_EN	= 0x1, 
			.RX_RATE = 0x2,
			.TX_RATE = 0x0
		}
};

const struct REG_REGCOMM REG2_config = {
		{
			.ADDR_MSB = 0x00,
			.ADDR_LSB = 0x02
		},
		{
			.DATA = 0x03
		}			
};

const struct REG_REGCOMM REG4_config = {
		{
			.ADDR_MSB = 0x00,
			.ADDR_LSB = 0x02
		},
		{
			.DATA = 0x43
		}			
};

const struct REG_REGCOMM REG3_config = {
		{
			.ADDR_MSB = 0x01,
			.ADDR_LSB = 0xEA
		},
		{
			.DATA = 0x05
		}			
};
#endif

void Deserializers_Config(void)
{
		delay(10000);	//test 10 ms

		Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&REG0_config.Addr, (uint8_t*)&REG0_config.Data);
	
		delay(5000);	//test 5 ms
		I2C_Disable(I2C0_ID);
		I2C_SetTargetAddr(I2C0_ID, 0x4C);
    I2C_Enable(I2C0_ID);
	
		//Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&REG2_config.Addr, (uint8_t*)&REG2_config.Data);	//0x02,0x03
		//Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&OLDI1_config.Addr, (uint8_t*)&OLDI1_config.Data);	//0x1CE,0x40
		//   Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&REG1_config.Addr, (uint8_t*)&REG1_config.Data);	//0x01,0x32
		//MstWriteRegister((uint8_t*)&REG1_config.Addr , 0x32);	//0x01,0x32
		//Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&REG3_config.Addr, (uint8_t*)&REG3_config.Data);	//0x1EA,0x05
		//Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&REG4_config.Addr, (uint8_t*)&REG4_config.Data);	//0x02,0x43
	
		//Ex_MstReadBuffer(I2C0_ID, (uint8_t*)&REG1_config.Addr);
}




