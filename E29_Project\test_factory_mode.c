/**************************************************************************************************/
/**
 * @file      : test_factory_mode.c
 * @brief     : 测试工厂模式功能
 * @version   : V1.0.0
 * @date      : 2024
 * <AUTHOR> 
 *
 * @note      : 这是一个测试文件，用于验证工厂模式进入和退出的正确性
 **************************************************************************************************/

#include <stdio.h>
#include <stdint.h>
#include <string.h>

// 工厂模式命令数据
uint8_t test_factory_enter_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFE,  // 进入工厂模式命令
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00         // 9个0x00，总共59个0x00
};

uint8_t test_factory_exit_cmd[64] = {
    0x1D, 0x29, 0x33, 0x88, 0xFF,  // 退出工厂模式命令
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00         // 9个0x00，总共59个0x00
};

// 工厂模式响应数据
uint8_t test_factory_enter_response[64] = {
    0x1D, 0x11,  // 进入成功响应
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00  // 2个0x00，总共62个0x00
};

uint8_t test_factory_exit_response[64] = {
    0x1D, 0x22,  // 退出成功响应
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  // 10个0x00
    0x00, 0x00  // 2个0x00，总共62个0x00
};

// 打印CAN数据包
void Print_Factory_CAN_Data(uint8_t *can_data, const char* test_name, uint32_t can_id)
{
    printf("\n=== %s ===\n", test_name);
    printf("CAN ID: 0x%03X\n", can_id);
    printf("Data: ");
    for(int i = 0; i < 64; i++) {
        printf("%02X ", can_data[i]);
        if((i + 1) % 16 == 0) printf("\n      ");
    }
    printf("\n");
    
    printf("关键字节:\n");
    printf("  Byte0: 0x%02X\n", can_data[0]);
    printf("  Byte1: 0x%02X\n", can_data[1]);
    if(can_data[2] != 0x00 || can_data[3] != 0x00 || can_data[4] != 0x00) {
        printf("  Byte2: 0x%02X\n", can_data[2]);
        printf("  Byte3: 0x%02X\n", can_data[3]);
        printf("  Byte4: 0x%02X\n", can_data[4]);
    }
}

// 验证数据格式
int Verify_Factory_Command(uint8_t *cmd_data, uint8_t expected_byte4)
{
    if(cmd_data[0] != 0x1D || cmd_data[1] != 0x29 || 
       cmd_data[2] != 0x33 || cmd_data[3] != 0x88 || 
       cmd_data[4] != expected_byte4) {
        return 0; // 验证失败
    }
    
    // 检查后续字节是否都为0x00
    for(int i = 5; i < 64; i++) {
        if(cmd_data[i] != 0x00) {
            return 0; // 验证失败
        }
    }
    
    return 1; // 验证成功
}

int Verify_Factory_Response(uint8_t *resp_data, uint8_t expected_byte1)
{
    if(resp_data[0] != 0x1D || resp_data[1] != expected_byte1) {
        return 0; // 验证失败
    }
    
    // 检查后续字节是否都为0x00
    for(int i = 2; i < 64; i++) {
        if(resp_data[i] != 0x00) {
            return 0; // 验证失败
        }
    }
    
    return 1; // 验证成功
}

// 运行测试
void Run_Factory_Mode_Tests(void)
{
    printf("工厂模式CAN数据包测试程序\n");
    printf("=====================================\n");
    
    // 测试1: 进入工厂模式命令
    Print_Factory_CAN_Data(test_factory_enter_cmd, "进入工厂模式命令", 0x5F0);
    if(Verify_Factory_Command(test_factory_enter_cmd, 0xFE)) {
        printf("✓ 进入命令格式验证通过\n");
    } else {
        printf("✗ 进入命令格式验证失败\n");
    }
    
    // 测试2: 进入工厂模式响应
    Print_Factory_CAN_Data(test_factory_enter_response, "进入工厂模式响应", 0x06F);
    if(Verify_Factory_Response(test_factory_enter_response, 0x11)) {
        printf("✓ 进入响应格式验证通过\n");
    } else {
        printf("✗ 进入响应格式验证失败\n");
    }
    
    // 测试3: 退出工厂模式命令
    Print_Factory_CAN_Data(test_factory_exit_cmd, "退出工厂模式命令", 0x5F0);
    if(Verify_Factory_Command(test_factory_exit_cmd, 0xFF)) {
        printf("✓ 退出命令格式验证通过\n");
    } else {
        printf("✗ 退出命令格式验证失败\n");
    }
    
    // 测试4: 退出工厂模式响应
    Print_Factory_CAN_Data(test_factory_exit_response, "退出工厂模式响应", 0x06F);
    if(Verify_Factory_Response(test_factory_exit_response, 0x22)) {
        printf("✓ 退出响应格式验证通过\n");
    } else {
        printf("✗ 退出响应格式验证失败\n");
    }
}

int main(void)
{
    Run_Factory_Mode_Tests();
    
    printf("\n测试完成!\n");
    printf("\n使用说明:\n");
    printf("1. 按Button1进入工厂模式 -> 发送0x5F0命令 -> 等待0x06F响应\n");
    printf("2. 在工厂模式下按Button1退出 -> 发送0x5F0命令 -> 等待0x06F响应\n");
    printf("3. Button2/Button3在工厂模式下控制显示屏参数\n");
    
    return 0;
}
