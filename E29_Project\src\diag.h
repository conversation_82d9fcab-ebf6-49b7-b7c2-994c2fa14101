/**************************************************************************************************/
/**
 * @file     diag.h
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#ifndef DIAG_H
#define DIAG_H


#include "I2C.h"
#include "pwm.h"



typedef enum
{
		Mode_On,
		Mode_Standby,
		Mode_Off
}DispWorkMode_t;

static void TFTAndPWMInit(void);
static void TFTAndPWMDeinit(void);

#endif /* DIAG_H */

