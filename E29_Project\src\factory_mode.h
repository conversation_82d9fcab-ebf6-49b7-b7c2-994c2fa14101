/**************************************************************************************************/
/**
 * @file      : factory_mode.h
 * @brief     : Factory mode management module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#ifndef FACTORY_MODE_H
#define FACTORY_MODE_H

#include <stdint.h>

/* Factory mode state enumeration */
typedef enum {
    FACTORY_MODE_NORMAL = 0,    /* Normal mode */
    FACTORY_MODE_ACTIVE = 1     /* Factory mode active */
} FactoryModeState_t;

/* Function declarations */
void Factory_Mode_Init(void);
void Enter_Factory_Mode(void);
void Exit_Factory_Mode(void);
void Handle_Factory_Mode_Response(uint8_t response_byte1);
FactoryModeState_t Get_Factory_Mode_State(void);

#endif /* FACTORY_MODE_H */
