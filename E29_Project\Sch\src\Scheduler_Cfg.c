#include "Scheduler.h"
#include "Scheduler_Cfg.h"
#include "i2c.h"
#include "uart.h"


#define DEV_SER         0x40u
#define DEV_DES         0x48u

static const I2cSendData au8SerHdmi[] = {
    {DEV_SER,0x20F5,0x00},

    {DEV_SER,0x2E00,0x00},
    {DEV_SER,0x2E01,0xff},
    {DEV_SER,0x2E02,0xff},
    {DEV_SER,0x2E03,0xff},
    {DEV_SER,0x2E04,0xff},
    {DEV_SER,0x2E05,0xff},
    {DEV_SER,0x2E06,0xff},
    {DEV_SER,0x2E07,0x00},
    {DEV_SER,0x2E08,0x0d},
    {DEV_SER,0x2E09,0xae},
    {DEV_SER,0x2E0A,0x04},
    {DEV_SER,0x2E0B,0x16},
    {DEV_SER,0x2E0C,0x00},
    {DEV_SER,0x2E0D,0x00},
    {DEV_SER,0x2E0E,0x00},
    {DEV_SER,0x2E0F,0x00},

    {DEV_SER,0x2E10,0x0b},
    {DEV_SER,0x2E11,0x1d},
    {DEV_SER,0x2E12,0x01},
    {DEV_SER,0x2E13,0x04},
    {DEV_SER,0x2E14,0xa5},
    {DEV_SER,0x2E15,0x24},
    {DEV_SER,0x2E16,0x14},
    {DEV_SER,0x2E17,0x78},
    {DEV_SER,0x2E18,0x02},
    {DEV_SER,0x2E19,0xee},
    {DEV_SER,0x2E1A,0x95},
    {DEV_SER,0x2E1B,0xa3},
    {DEV_SER,0x2E1C,0x54},
    {DEV_SER,0x2E1D,0x4c},
    {DEV_SER,0x2E1E,0x99},
    {DEV_SER,0x2E1F,0x26},

    {DEV_SER,0x2E20,0x0f},
    {DEV_SER,0x2E21,0x50},
    {DEV_SER,0x2E22,0x54},
    {DEV_SER,0x2E23,0x00},
    {DEV_SER,0x2E24,0x00},
    {DEV_SER,0x2E25,0x00},
    {DEV_SER,0x2E26,0x01},
    {DEV_SER,0x2E27,0x01},
    {DEV_SER,0x2E28,0x01},
    {DEV_SER,0x2E29,0x01},
    {DEV_SER,0x2E2A,0x01},
    {DEV_SER,0x2E2B,0x01},
    {DEV_SER,0x2E2C,0x01},
    {DEV_SER,0x2E2D,0x01},
    {DEV_SER,0x2E2E,0x01},
    {DEV_SER,0x2E2F,0x01},

    {DEV_SER,0x2E30,0x01},
    {DEV_SER,0x2E31,0x01},
    {DEV_SER,0x2E32,0x01},
    {DEV_SER,0x2E33,0x01},
    {DEV_SER,0x2E34,0x01},
    {DEV_SER,0x2E35,0x01},
    {DEV_SER,0x2E36,0x9C},
    {DEV_SER,0x2E37,0x09},
    {DEV_SER,0x2E38,0x40},
    {DEV_SER,0x2E39,0x3D},
    {DEV_SER,0x2E3A,0x60},
    {DEV_SER,0x2E3B,0xb6},
    {DEV_SER,0x2E3C,0x41},
    {DEV_SER,0x2E3D,0x00},
    {DEV_SER,0x2E3E,0x2D},
    {DEV_SER,0x2E3F,0x0C},

    {DEV_SER,0x2E40,0xC3},
    {DEV_SER,0x2E41,0x0C},
    {DEV_SER,0x2E42,0xc8},
    {DEV_SER,0x2E43,0x64},
    {DEV_SER,0x2E44,0x00},
    {DEV_SER,0x2E45,0x00},
    {DEV_SER,0x2E46,0x00},
    {DEV_SER,0x2E47,0x16},
    {DEV_SER,0x2E48,0x00},
    {DEV_SER,0x2E49,0x00},
    {DEV_SER,0x2E4A,0x00},
    {DEV_SER,0x2E4B,0xfe},
    {DEV_SER,0x2E4C,0x00},
    {DEV_SER,0x2E4D,0x20},
    {DEV_SER,0x2E4E,0x20},
    {DEV_SER,0x2E4F,0x20},

    {DEV_SER,0x2E50,0x20},
    {DEV_SER,0x2E51,0x20},
    {DEV_SER,0x2E52,0x20},
    {DEV_SER,0x2E53,0x20},
    {DEV_SER,0x2E54,0x20},
    {DEV_SER,0x2E55,0x20},
    {DEV_SER,0x2E56,0x20},
    {DEV_SER,0x2E57,0x20},
    {DEV_SER,0x2E58,0x20},
    {DEV_SER,0x2E59,0x20},
    {DEV_SER,0x2E5A,0x00},
    {DEV_SER,0x2E5B,0x00},
    {DEV_SER,0x2E5C,0x00},
    {DEV_SER,0x2E5D,0xfe},
    {DEV_SER,0x2E5E,0x00},
    {DEV_SER,0x2E5F,0x20},

    {DEV_SER,0x2E60,0x20},
    {DEV_SER,0x2E61,0x20},
    {DEV_SER,0x2E62,0x20},
    {DEV_SER,0x2E63,0x20},
    {DEV_SER,0x2E64,0x20},
    {DEV_SER,0x2E65,0x20},
    {DEV_SER,0x2E66,0x20},
    {DEV_SER,0x2E67,0x20},
    {DEV_SER,0x2E68,0x20},
    {DEV_SER,0x2E69,0x20},
    {DEV_SER,0x2E6A,0x20},
    {DEV_SER,0x2E6B,0x20},
    {DEV_SER,0x2E6C,0x00},
    {DEV_SER,0x2E6D,0x00},
    {DEV_SER,0x2E6E,0x00},
    {DEV_SER,0x2E6F,0xfe},

    {DEV_SER,0x2E70,0x00},
    {DEV_SER,0x2E71,0x20},
    {DEV_SER,0x2E72,0x20},
    {DEV_SER,0x2E73,0x20},
    {DEV_SER,0x2E74,0x20},
    {DEV_SER,0x2E75,0x20},
    {DEV_SER,0x2E76,0x20},
    {DEV_SER,0x2E77,0x20},
    {DEV_SER,0x2E78,0x20},
    {DEV_SER,0x2E79,0x20},
    {DEV_SER,0x2E7A,0x20},
    {DEV_SER,0x2E7B,0x20},
    {DEV_SER,0x2E7C,0x20},
    {DEV_SER,0x2E7D,0x20},
    {DEV_SER,0x2E7E,0x00},
    {DEV_SER,0x2E7F,0xc8},
    
    {DEV_SER,0x20F5,0x01},
    {DEV_SER,0x0001,0xD8},

};

static const I2cSendData au8SerVPGCheckerBoard[] = {
    {DEV_SER,0x0053,0x00},
    {DEV_SER,0x01e5,0x01},
    {DEV_SER,0x01C8,0xE3},
    {DEV_SER,0x01CA,0x00},
    {DEV_SER,0x01CB,0x00},
    {DEV_SER,0x01CC,0x00},
    {DEV_SER,0x01CD,0x00},
    {DEV_SER,0x01CE,0x13},
    {DEV_SER,0x01CF,0x77},
    {DEV_SER,0x01D0,0x06},
    {DEV_SER,0x01D1,0x2f},
    {DEV_SER,0x01D2,0x24},
    {DEV_SER,0x01D3,0x00},
    {DEV_SER,0x01D4,0x00},
    {DEV_SER,0x01D5,0x00},
    {DEV_SER,0x01D6,0x00},
    {DEV_SER,0x01D7,0x08},
    {DEV_SER,0x01D8,0x06},
    {DEV_SER,0x01D9,0x75},
    {DEV_SER,0x01DA,0x00},
    {DEV_SER,0x01DB,0xF7},
    {DEV_SER,0x01DC,0x00},
    {DEV_SER,0x01DD,0x20},
    {DEV_SER,0x01DE,0x81},
    {DEV_SER,0x01DF,0x06},
    {DEV_SER,0x01E0,0x40},
    {DEV_SER,0x01E1,0x00},
    {DEV_SER,0x01E2,0x3D},
    {DEV_SER,0x01E3,0x00},
    {DEV_SER,0x01E4,0xB6},
    {DEV_SER,0x01E5,0x01},
    {DEV_SER,0x01E6,0x03},
    {DEV_SER,0x01E7,0xff},
    {DEV_SER,0x01E8,0x00},
    {DEV_SER,0x01E9,0x00},
    {DEV_SER,0x01EA,0xff},
    {DEV_SER,0x01EB,0x00},
    {DEV_SER,0x01EC,0x00},
    {DEV_SER,0x01ED,0xf0},
    {DEV_SER,0x01EE,0xf0},
    {DEV_SER,0x01EF,0x78},
    {DEV_SER,0x01C9,0x19},
    {DEV_SER,0x0001,0xd8},
};

//改动
static const I2cSendData au8SerVPGColorBar[] = {
    {DEV_SER,0x0053,0x00},
    {DEV_SER,0x01e5,0x02},
    {DEV_SER,0x01C8,0xE3},
    {DEV_SER,0x01CA,0x00},
    {DEV_SER,0x01CB,0x00},
    {DEV_SER,0x01CC,0x00},
    {DEV_SER,0x01CD,0x00},
    {DEV_SER,0x01CE,0x13},
    {DEV_SER,0x01CF,0x77},
    {DEV_SER,0x01D0,0x06},
    {DEV_SER,0x01D1,0x2f},
    {DEV_SER,0x01D2,0x24},
    {DEV_SER,0x01D3,0x00},
    {DEV_SER,0x01D4,0x00},
    {DEV_SER,0x01D5,0x00},
    {DEV_SER,0x01D6,0x00},
    {DEV_SER,0x01D7,0x08},
    {DEV_SER,0x01D8,0x06},
    {DEV_SER,0x01D9,0x75},
    {DEV_SER,0x01DA,0x00},
    {DEV_SER,0x01DB,0xF7},
    {DEV_SER,0x01DC,0x00},
    {DEV_SER,0x01DD,0x20},
    {DEV_SER,0x01DE,0x81},
    {DEV_SER,0x01DF,0x06},
    {DEV_SER,0x01E0,0x40},
    {DEV_SER,0x01E1,0x00},
    {DEV_SER,0x01E2,0x3D},
    {DEV_SER,0x01E3,0x00},
    {DEV_SER,0x01E4,0xB6},
    {DEV_SER,0x01E5,0x02},
    {DEV_SER,0x01E6,0x03},
    {DEV_SER,0x01C9,0x19},
    {DEV_SER,0x0001,0xd8},
};

static const I2cSendData au8DesHdmi[] = {
    {DEV_DES, 0x0001, 0x32},
    {DEV_DES, 0x0050, 0x00},
    {DEV_DES, 0x0D03, 0x8A},//展频 0.5
    {DEV_DES, 0x01CE, 0xC7},
    {DEV_DES, 0x01CF, 0x09},
    {DEV_DES, 0x020C ,0x03},
	{DEV_DES, 0x020D ,0xA8},
	{DEV_DES, 0x020E ,0x48},
};

uint32_t u32Task1value      = 0u;
uint32_t u32Task2value      = 0u;
uint32_t u32Task3value      = 0u;
uint32_t u32Task4value      = 0u;
uint32_t u32Task5value      = 0u;
uint32_t u32Task6value      = 0u;
uint32_t u32Task7value      = 0u;
uint32_t u32Task8value      = 0u;
uint32_t u32Task9value      = 0u;
uint32_t u32Task10value     = 0u;
uint32_t u32Task11value     = 0u;
uint32_t u32Task12value     = 0u;
uint32_t u32Task13value     = 0u;
bool     GpioFlag = true;

void Task1(void)
{

}

void Task2(void)
{
	
}

void Task3(void)
{
	
}

void Task4(void)
{
	
}

void Task5(void)
{
	
}

void Task6(void)
{
	
}

void Task7(void)
{
	
}

void Task8(void)
{
	
}

void Task9(void)
{

}

void Task10(void)
{

}

extern void GPIOIntInit(void);

extern uint8_t u8RecvDuration;

// update timer
static uint16_t u16UpdateTimer = 0;
bool boTimer_200MsFlag = false;
bool boTimer_2MsFlag = false;
bool boTimer_100MsFlag = false;
bool boTimer_5MsFlag = false; 

uint8_t u8InitFlag = 0u;
uint8_t u8InitFlagSer = 0u;

void Task11(void)           //1msTask
{
    uint8_t au8RegData[8u] = {1u, 0u, 0x32u, 0u, 0u, 0u, 0u, 0u};
    static uint16_t u16Index = 0u;
    static uint16_t u16TimeWait5S = 3u;

    if(u8RecvDuration)          //串口计数用的
    {
        u8RecvDuration -= 1u;
    }

	u32Task11value ++;
	if(u32Task11value >= 10u)
	{
		u32Task11value = 0u;
        /* SER init */
        // if(GPIO_ReadPinLevel(PORT_C , GPIO_5) == GPIO_LOW)
        // {
        //     u16Index = 0u;
        //     u16TimeWait5S = 50;
        //     if(u8InitFlag || u8InitFlagSer)
        //     {
        //         NVIC_SystemReset();
        //     }
        // }
        // else if(GPIO_ReadPinLevel(PORT_C , GPIO_5) == GPIO_HIGH && u16TimeWait5S)
        // {
            
        //     u16TimeWait5S --;
        //     if(!u16TimeWait5S)
        //     {
        //         I2C_Disable(I2C0_ID);
        //         I2C_SetTargetAddr(I2C0_ID, DEV_SER);
        //         I2C_Enable(I2C0_ID);
        //     }
        // }
        // else if(!u8InitFlag)
        // {
        //     u8InitFlagSer = 1;
        //     if (u16Index < 131)
        //     {
        //         Ex_MstWriteBuffer(I2C0_ID, (uint8_t *)&au8SerHdmi[u16Index].u16DstRegAddr, (uint8_t *)&au8SerHdmi[u16Index].u8RegData);
        //         u16Index += 1u;
        //     }
        //     else if(u16Index >= 131)
        //     {
        //         /* SER init finished, start to init DES */
        //         u16Index = 0u;
        //         u8InitFlag = 1u;
        //         I2C_Disable(I2C0_ID);
        //         I2C_SetTargetAddr(I2C0_ID, DEV_DES);
        //         I2C_Enable(I2C0_ID);
        //     }
        //     else
        //     {
        //         /* Do nothing */
        //     }
        // }
        // /* Des init */
        // else if(u8InitFlag == 1u)
        // {
        //     if(u16Index < 8u)
        //     {
        //         Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&au8DesHdmi[u16Index].u16DstRegAddr, (uint8_t*)&au8DesHdmi[u16Index].u8RegData);
        //         u16Index += 1u;
        //     }
        //     else if(u16Index >= 8u)
        //     {
        //         /* Des init finished, start to read SER reg */
        //         u16Index = 0u;
        //         u8InitFlag = 2u;
        //         I2C_Disable(I2C1_ID);
        //         I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
        //         I2C_Enable(I2C1_ID);
        //     }
        //     else
        //     {
        //         /* Do nothing */
        //     }
        // }
        // else if(u8InitFlag == 2u)
        // {
        //     u8InitFlag = 3u;
        //     au8RegData[7u] = au8RegData[0u]^au8RegData[1u]^au8RegData[2u]^au8RegData[3u]^au8RegData[4u]^au8RegData[5u]^au8RegData[6u];
        //     Ex_MstWriteArray(I2C1_ID, (uint8_t*)&au8RegData[0u]);
        // }
        // else if(u8InitFlag == 3u)
        // {
        //     u8InitFlag = 4u;
        //     GPIOIntInit();
        // }
        // else if(u8InitFlag == 4u)
        // {
        //     u8InitFlag = 5u;
        //     u8StartEOLTest = 1;
        // }
        // else
        // {

        // }

        if(GpioFlag)
        {
            GPIOIntInit();
            GpioFlag=false;
        }
        
        u8InitFlag = 5u;
        App_vMain();
	}

    //升级的计时器
    if(boTimer_200ms)
    {
        u16UpdateTimer++;
        if(u16UpdateTimer > 200)
        {
            u16UpdateTimer = 0;
            boTimer_200ms = false;
            boTimer_200MsFlag = true;
        }
    }
    else if(boTimer_2ms)
    {
        u16UpdateTimer++;
        if(u16UpdateTimer > 2)
        {
            u16UpdateTimer = 0;
            boTimer_2ms = false;
            boTimer_2MsFlag = true;
        }
    }
    else if(boTimer_100ms)
    {
        u16UpdateTimer++;
        if(u16UpdateTimer > 100)
        {
            u16UpdateTimer = 0;
            boTimer_100ms = false;
            boTimer_100MsFlag = true;
        }
    }
    else if(boTimer_5ms)
    {
        u16UpdateTimer++;
        if(u16UpdateTimer > 50)
        {
            u16UpdateTimer = 0;
            boTimer_5ms = false;
            boTimer_5MsFlag = true;
        }
    }

    if(u8UartUpdateState == Updating)
    {
        App_vUpdateFraneSend();
    }

}

void Task12(void)
{
    
}

void Task13(void)
{

}

const Sch_Task_Info_T Sch_RunTaskTable[] =
{
	/* always online tasks: ------------------------------------------*/
	{&Task12,              SCH_MS( 0u),            SCH_TIMEDURATION( 0u)},
	{SCH_EndOfList,        SCH_MS( 1u),            SCH_TIMEDURATION( 0u)},
	/* 1ms periodic tasks: -------------------------------------------*/
	{&Task11,              SCH_MS( 0u),            SCH_TIMEDURATION( 1u)},
	{SCH_EndOfList,        SCH_MS( 1u),            SCH_TIMEDURATION( 1u)},
	/* 10ms periodic tasks: -------------------------------------------*/
	{&Task1,               SCH_MS( 0u),            SCH_TIMEDURATION( 1u)},
	{&Task2,               SCH_MS( 1u),            SCH_TIMEDURATION( 2u)},
	{&Task3,               SCH_MS( 2u),            SCH_TIMEDURATION( 3u)},
	{&Task4,               SCH_MS( 3u),            SCH_TIMEDURATION( 4u)},
	{&Task5,               SCH_MS( 4u),            SCH_TIMEDURATION( 5u)},
	{&Task6,               SCH_MS( 5u),            SCH_TIMEDURATION( 5u)},
	{&Task7,               SCH_MS( 6u),            SCH_TIMEDURATION( 7u)},
	{&Task8,               SCH_MS( 7u),            SCH_TIMEDURATION( 8u)},
	{&Task9,               SCH_MS( 8u),            SCH_TIMEDURATION( 9u)},
	{&Task10,              SCH_MS( 9u),            SCH_TIMEDURATION(10u)},
	{SCH_EndOfList,        SCH_MS(10u),            SCH_TIMEDURATION(10u)},
    /* 20ms periodic tasks: -------------------------------------------*/
	{&Task13,              SCH_MS( 0u),            SCH_TIMEDURATION(20u)},
	{SCH_EndOfList,        SCH_MS(20u),            SCH_TIMEDURATION(20u)},
	{SCH_EndOfList,        SCH_MS( 0u),            SCH_TIMEDURATION( 0u)}
};
