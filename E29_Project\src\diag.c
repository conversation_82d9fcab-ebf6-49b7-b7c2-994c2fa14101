/**************************************************************************************************/
/**
 * @file     diag.c
 * @brief    
 * @version  V1.0.0
 * @date     December-2023
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#include "diag.h"

// #define MAX_DERATING_TEMP_90 		90
// #define MID_DERATING_TEMP_85 		85
// #define MIN_DERATING_TEMP_65 		65

// const uint8_t HardwareVersion[14] = {0x00,0x01,0x00,0x01,0x00};
// const uint8_t SoftwareVersion[14] = {0x00,0x01,0x00,0x01,0x00};
// const uint8_t PartNum[30] =  {0x00,0x01,0x00,0x01,0x00};
// const uint8_t ProtocolNum[14] =  {0x00,0x01,0x00,0x01,0x00};

// extern uint8_t DiagRecvBuf[];					//the master write data

// static uint8_t DispMode = Mode_Standby;
// static bool PWMDeinitFlag = false;

// static uint8_t ScreenBLTemp = 0;
// static uint8_t PCBTemp = 0;

// /*read*/
// extern bool DiagModeReqFlag;       //0x00
// extern bool DiagBackLightReqFlag;  //0x02
// //0x04
// //0x06
// extern bool DiagHardwareReqFlag;   //0x07
// extern bool DiagSoftwareReqFlag;   //0x08
// extern bool DiagPartNumReqFlag;    //0x09
// extern bool DiagProtocolReqFlag;   //0x0A
// extern bool DiagTempReqFlag;       //0xF0

// /*write*/
// extern bool DiagModeCtrlFlag;      //0x01
// //0x03
// //0x05




// void BackLightDeratingControl(uint8_t Temperature,uint8_t MStlevel,uint8_t CurrentLevel)
// {
// 		if(Temperature > MAX_DERATING_TEMP_90)
// 		{
// 				Ex_PWMChangeDutyCycle(0);
// 		}
// 		else if(Temperature > MID_DERATING_TEMP_85)
// 		{
// 				CurrentLevel = CurrentLevel / 10;
// 				Ex_PWMChangeDutyCycle(MIN(CurrentLevel, MStlevel));
// 		}
// 		else if(Temperature >= MIN_DERATING_TEMP_65)
// 		{
// 				CurrentLevel = (360 - 4 * CurrentLevel) * CurrentLevel / 100;
// 				Ex_PWMChangeDutyCycle(MIN(CurrentLevel, MStlevel));
// 		}
// 		else
// 		{
// 				// do nothing
// 		}
// }

// static void TFTAndPWMInit(void)
// {
// 		PWMDeinitFlag = true;

// 		/* pull dowm TFT and light the backlight	*/
// 		PORT_PinmuxConfig(PORT_C, GPIO_9, PTC9_GPIO);
// 		GPIO_SetPinDir(PORT_C, GPIO_9, GPIO_OUTPUT);
// 		GPIO_ClearPinOutput(PORT_C, GPIO_9);	//MCU_LCD_EN(TFT_EN)		
	
// 		PORT_PinmuxConfig(PORT_C, GPIO_3, PTC3_GPIO);
// 		GPIO_SetPinOutput(PORT_C, GPIO_3);	//MCU_LCD_RST

// 		PORT_PinmuxConfig(PORT_E, GPIO_8, PTE8_GPIO);
// 		GPIO_SetPinOutput(PORT_E, GPIO_8);	//MCU_PON

// 		PORT_PinmuxConfig(PORT_C, GPIO_7, PTC7_GPIO);
// 		GPIO_SetPinDir(PORT_C, GPIO_7, GPIO_OUTPUT);
// 		GPIO_SetPinOutput(PORT_C, GPIO_7);	//MCU_BLK_EN

// 		Pwm_Init();
// }

// static void TFTAndPWMDeinit(void)
// {
// 		if(PWMDeinitFlag)
// 		{
// 				Ex_PWMChangeDutyCycle(0);

// 				GPIO_SetPinOutput(PORT_C, GPIO_9);		//MCU_LCD_EN(TFT_EN)
// 				GPIO_ClearPinOutput(PORT_C, GPIO_3);	//MCU_LCD_RST
// 				GPIO_ClearPinOutput(PORT_E, GPIO_8);	//MCU_PON
// 				GPIO_ClearPinOutput(PORT_C, GPIO_7);	//MCU_BLK_EN
// 		}
// }
