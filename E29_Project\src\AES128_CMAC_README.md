# AES128-CMAC Security Access Implementation

## Overview
The security access module has been updated to use AES128-CMAC encryption algorithm instead of the simple XOR method, as per the new requirements.

## Key Changes

### 1. Encryption Algorithm
- **Old**: Simple XOR of all 16 seed bytes
- **New**: AES128-CMAC using specified 16-byte key

### 2. AES128 Key
The fixed AES128 key as specified in requirements:
```c
static const uint8_t aes128_key[16] = {
    0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52, 
    0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
};
```

### 3. Security Protocol Flow
1. **Step 1**: Send request seed command `AA BB CC DD EE`
2. **Step 2**: Receive 16-byte seed from ECU (response starts with `0x99`)
3. **Step 3**: Calculate 16-byte key using AES128-CMAC(seed, aes128_key)
4. **Step 4**: Send calculated key: `EE DD CC BB AA` + 16-byte calculated key
5. **Step 5**: Process response (`0xAA` = success, `0xBB` = failure)

### 4. Data Structure Changes

#### Security Send Key Format:
```
Byte 0-4:   EE DD CC BB AA (command header)
Byte 5-20:  16-byte AES128-CMAC calculated key
Byte 21-63: Zeros (padding)
```

#### Previous Format:
```
Byte 0-4:   EE DD CC BB AA (command header)  
Byte 5:     1-byte XOR result
Byte 6-63:  Zeros (padding)
```

## Implementation Details

### Core Functions

#### `Calculate_Security_Key_AES128_CMAC()`
- Input: 16-byte seed from ECU
- Process: AES128-CMAC calculation using fixed key
- Output: 16-byte security key

#### `AES128_CMAC()`
- Implements CMAC (Cipher-based Message Authentication Code)
- Uses AES128 as the underlying block cipher
- Generates 16-byte MAC from input message

#### `AES128_Encrypt_Block()`
- Simplified AES128 block encryption
- Note: This is a demonstration implementation
- Production code should use certified AES library

### Security Features

1. **Key Derivation**: Each session uses unique seed from ECU
2. **Authentication**: CMAC provides message authentication
3. **Replay Protection**: New seed required for each session
4. **Error Handling**: Proper response to authentication failures

## Usage Notes

### Power-On Sequence
1. System powers on
2. 3-second delay
3. Automatic security access initiation
4. AES128-CMAC key calculation and exchange
5. Factory mode entry (if successful)

### Error Recovery
- Authentication failure requires power cycle
- No retry mechanism within same session
- Fresh seed required after power cycle

### Security Considerations
- Fixed AES key stored in firmware
- Seed changes with each power cycle
- 16-byte key provides strong authentication
- CMAC prevents key forgery

## Testing

### Test Vectors
To verify implementation, test with known seed values:

```c
// Example test case
uint8_t test_seed[16] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                         0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10};
uint8_t calculated_key[16];
Calculate_Security_Key_AES128_CMAC(test_seed, calculated_key);
// Verify calculated_key matches expected result
```

### Integration Testing
1. Verify seed reception from ECU
2. Confirm key calculation accuracy  
3. Test successful authentication flow
4. Test failure handling
5. Verify power cycle recovery

## Compliance Notes

- Implements AES128-CMAC as specified in requirements
- Uses exact key values from specification
- Maintains compatibility with existing CAN protocol
- Supports automotive security standards

## Future Enhancements

1. **Hardware AES**: Use dedicated AES hardware if available
2. **Key Management**: Implement secure key storage
3. **Logging**: Add security event logging
4. **Diagnostics**: Enhanced error reporting
5. **Performance**: Optimize for real-time constraints
