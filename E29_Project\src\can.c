/**************************************************************************************************/
/**
 * @file     can.c
 * @brief    
 * @version  V1.0.0
 * @date     June-2024
 * <AUTHOR>
 * @note
 * Copyright (C) 2021-2023 Zhixin Semiconductor Ltd. All rights reserved.
 *
 **************************************************************************************************/
 
#include "can.h"
 

static CAN_MsgBuf_t rx_buf;

uint8_t au8CanData[64u];
uint8_t au8CanDataHW[16u] ;
uint8_t au8CanDataSN[32u] ;
uint8_t u8CanReadData;
uint8_t u8CanVersion;

static CAN_Config_t Can_cfg =
{
    .mbMaxNum = 14u,
    .rxFifoEn = DISABLE,
    .rxFifoIdFilterNum = CAN_RX_FIFO_ID_FILTERS_8,
    .transferType = CAN_RXFIFO_INTERRUPTS,  /* Use interrupts for RxFIFO */
    .mode = CAN_MODE_NORMAL,
    .fdEn = ENABLE,                    /* Disable CANFD for classic CAN compatibility */
    .bitTiming =
    {
      .propSeg = 4,
      .phaseSeg1 = 3,
      .phaseSeg2 = 2,
      .preDivider = 4,
      .rJumpwidth = 1
    },
    .payload0 = CAN_PAYLOAD_SIZE_64,     /* Set payload size to 8 bytes for classic CAN */
    .payload1 = CAN_PAYLOAD_SIZE_64,     /* Set payload size to 8 bytes for classic CAN */

    .bitTimingFdData =                  /* CANFD data phase timing (not used in classic mode) */
    {
      .propSeg = 4,
      .phaseSeg1 = 3,
      .phaseSeg2 = 2,
      .preDivider = 1,                  /* Faster data rate for FD data phase */
      .rJumpwidth = 1
    },
};

CAN_MessageInfo_t tx_info =
{
    .idType = CAN_MSG_ID_STD,   /*msg type: standard frame*/
    .dataLen = 64,               /*date length: 8 bytes for classic CAN*/
    .remoteFlag = RESET,        /*isn't remote frame */
    .fdEn = ENABLE,           /*Disable FD for classic CAN compatibility*/
    .fdPadding = 0,             /*Use zeros for FD padding*/
    .brsEn = ENABLE           /*Bit rate switch disabled for classic CAN*/
};


CAN_MessageInfo_t rx_info =
{
    .idType = CAN_MSG_ID_STD,   /*msg type: standard frame*/
    .dataLen = 64,               /*date length*/
    .remoteFlag = RESET,        /*isn't remote frame */
    .fdEn = ENABLE,            /*Enable FD for 64-byte reception*/
    .fdPadding = 0,             /*Use zeros for FD padding*/
    .brsEn = ENABLE           /*Bit rate switch disabled*/
};

void CAN_Send_Msg(uint32_t msgId, const uint8_t *msgData)
{
    CAN_Send(CAN_ID_0, 10, &tx_info, msgId, msgData);
}


void CAN_Send_Button1_Msg(uint32_t msgId, const uint8_t *msgData)
{
    
    CAN_Send(CAN_ID_0, 10, &tx_info, msgId, msgData);
}

void CAN_Send_Button2_Msg(uint32_t msgId, const uint8_t *msgData)
{
   
    CAN_Send(CAN_ID_0, 11, &tx_info, msgId, msgData);
}

void CAN_Send_Button3_Msg(uint32_t msgId, const uint8_t *msgData)
{
   
    CAN_Send(CAN_ID_0, 9, &tx_info, msgId, msgData);
}

void CAN_Send_FactoryMode_Msg(uint32_t msgId, const uint8_t *msgData)
{
   
    CAN_Send(CAN_ID_0, 13, &tx_info, msgId, msgData);
}


extern void Handle_Factory_Mode_Response(uint8_t response_byte1);

extern void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len);

void Mb_Callback(uint32_t mbIdx)
{
    CAN_GetMsgBuff(CAN_ID_0, mbIdx, &rx_buf);
    
    if(mbIdx == 0)  // 邮箱0：接收0x06F响应
    {
        // 检查是否为0x06F响应
        if(rx_buf.msgId == 0x06F)
        {
            Handle_Security_Access_Response(rx_buf.data, rx_buf.dataLen);
            // 保留原工厂模式处理
            if(rx_buf.data[0] == 0x1D)
            {
                Handle_Factory_Mode_Response(rx_buf.data[1]);
            }
        }
    }
   
}

void CANConfig_Init(void)
{
    /* Set module clock */
    CLK_ModuleSrc(CLK_CAN0, CLK_SRC_OSC40M);
    CLK_SetClkDivider(CLK_CAN0, CLK_DIV_2);

    /* Enable module */
    SYSCTRL_ResetModule(SYSCTRL_CAN0);
    SYSCTRL_EnableModule(SYSCTRL_CAN0);

    /* Configure CAN pins FIRST before CAN init */
    PORT_PinmuxConfig(PORT_A, GPIO_11, PTA11_CAN0_TX);
    PORT_PinmuxConfig(PORT_A, GPIO_10, PTA10_CAN0_RX);

    /* Init CAN */
    CAN_Init(CAN_ID_0, &Can_cfg);

    /* Install callback and enable mailbox 0 interrupt for receiving */
    CAN_InstallMbCallBackFunc(CAN_ID_0, Mb_Callback);
    CAN_MbIntMask(CAN_ID_0, 0, UNMASK);

    /* Enable CAN interrupts in NVIC */
    NVIC_SetPriority(CAN0_IRQn, 0x1);
    NVIC_EnableIRQ(CAN0_IRQn);

    /* Configure CAN FD timing compensation */
    CAN_FdTdcEnable(CAN_ID_0, 8);

    /* Set individual mask for each mailbox */
    CAN_SetRxMaskType(CAN_ID_0, CAN_RX_MASK_INDIVIDUAL);

    /* Configure mailbox 0 to receive factory mode response (ID: 0x06F) */
    /* 设置接收掩码：0x7FF表示精确匹配ID */
    CAN_SetRxMbIndividualMask(CAN_ID_0, CAN_MSG_ID_STD, 0, 0x7FF);
    CAN_MbReceive(CAN_ID_0, 0, &rx_info, 0x06F);

    /* Enable CAN controller */
    CAN_Enable(CAN_ID_0);
}


